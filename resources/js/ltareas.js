/**
 * Helper function to render note icon when a task has notes
 * @param {string|null} nota The note content
 * @returns {string} HTML for the note icon or empty string
 */
function renderNoteIcon(nota) {
    if (nota && nota.trim() !== '' && nota !== 'N/A') {
        return ' <i class="fa fa-circle-exclamation text-info ms-1" title="Esta tarea tiene notas" style="font-size: 0.9em;"></i>';
    }
    return '';
}

document.addEventListener('DOMContentLoaded', function () {
    // Use event delegation on the table body
    const tableBody = document.getElementById('tarea-table-body');
    
    if (tableBody) {
        tableBody.addEventListener('click', function (event) {
            const marcarTerminadaButton = event.target.closest('.btn-marcar-terminada');
            const marcarEnProgresoButton = event.target.closest('.btn-marcar-en-progreso');
            const eliminarButton = event.target.closest('.btn-eliminar-tarea');
            const verAgentesButton = event.target.closest('.btn-ver-agentes');
            const editarTareaButton = event.target.closest('.btn-editar-tarea');
            const toggleButton = event.target.closest('.btn-toggle-hijas');
            const verNotaSpan = event.target.closest('.ver-nota');
            
            // --- Handle Mark as Complete Click ---
            if (marcarTerminadaButton) {
                event.preventDefault();
                const tareaId = marcarTerminadaButton.dataset.tareaid;
                
                swal({
                    title: "Confirmar Acción",
                    text: "¿Seguro que quieres marcar esta tarea como terminada?",
                    icon: "warning",
                    buttons: {
                        cancel: {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
                        confirm: {text: "Marcar como Terminada", value: true, visible: true, className: "btn-success", closeModal: true}
                    },
                }).then((willComplete) => {
                    if (willComplete) {
                        performAjaxAction('marcar_terminada', tareaId);
                    }
                });
            }
            
            // --- Handle Mark as In Progress Click ---
            if (marcarEnProgresoButton) {
                event.preventDefault();
                const tareaId = marcarEnProgresoButton.dataset.tareaid;
                
                swal({
                    title: "Confirmar Acción",
                    text: "¿Seguro que quieres marcar esta tarea como en progreso?",
                    icon: "warning",
                    buttons: {
                        cancel: {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
                        confirm: {text: "Marcar como En Progreso", value: true, visible: true, className: "btn-info", closeModal: true}
                    },
                }).then((willProgress) => {
                    if (willProgress) {
                        performAjaxAction('marcar_en_progreso', tareaId);
                    }
                });
            }
            
            // --- Handle Delete Click ---
            if (eliminarButton) {
                event.preventDefault();
                const tareaId = eliminarButton.dataset.tareaid;
                
                swal({
                    title: "Confirmar Eliminación",
                    text: "¿Seguro que quieres eliminar esta tarea? Esta acción no se puede deshacer.",
                    icon: "warning",
                    buttons: {
                        cancel: {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
                        confirm: {text: "Eliminar", value: true, visible: true, className: "btn-danger", closeModal: true}
                    },
                    dangerMode: true,
                }).then((willDelete) => {
                    if (willDelete) {
                        performAjaxAction('eliminar', tareaId);
                    }
                });
            }
            
            // --- Handle Toggle Child Tasks Click ---
            if (toggleButton) {
                event.preventDefault();
                const parentId = toggleButton.dataset.parentId;
                const childRows = document.querySelectorAll(`tr.tarea-hija[data-parent-id="${parentId}"]`);
                const icon = toggleButton.querySelector('i');
                
                // Toggle visibility of child rows
                let isCollapsed = toggleButton.classList.contains('collapsed');
                
                childRows.forEach(row => {
                    if (isCollapsed) {
                        // Show child rows
                        row.style.display = '';
                    } else {
                        // Hide child rows
                        row.style.display = 'none';
                    }
                });
                
                // Toggle button state
                if (isCollapsed) {
                    toggleButton.classList.remove('collapsed');
                    icon.className = 'fa fa-chevron-down';
                } else {
                    toggleButton.classList.add('collapsed');
                    icon.className = 'fa fa-chevron-right';
                }
                
                // Save expanded states to localStorage
                saveExpandedStatesToLocalStorage();
            }
            
            // --- Handle Ver Agentes Click ---
            if (verAgentesButton) {
                event.preventDefault();
                
                const tareaId = verAgentesButton.dataset.tareaid;
                const tareaDescripcion = verAgentesButton.dataset.tareadescripcion;
                
                // Store current task ID for modal operations
                window.currentTareaId = tareaId;
                
                // Set the task description in the modal
                document.getElementById('modal-tarea-descripcion').textContent = tareaDescripcion;
                
                // Show loading state
                document.getElementById('agentes-loading').style.display = 'block';
                document.getElementById('agentes-content').style.display = 'none';
                document.getElementById('agentes-error').style.display = 'none';
                
                // Load agentes dropdown and existing agentes data
                Promise.all([
                    loadAgentesDropdown(),
                    loadTareaAgentes(tareaId)
                ]).then(() => {
                    // Hide loading state
                    document.getElementById('agentes-loading').style.display = 'none';
                    document.getElementById('agentes-content').style.display = 'block';
                }).catch(error => {
                    console.error('Error loading modal data:', error);
                    // Hide loading state
                    document.getElementById('agentes-loading').style.display = 'none';
                    document.getElementById('agentes-content').style.display = 'block';
                    // Show error message
                    document.getElementById('agentes-error').textContent = 'Error al cargar los datos: ' + error.message;
                    document.getElementById('agentes-error').style.display = 'block';
                });
            }
            
            // --- Handle Edit Task Click ---
            if (editarTareaButton) {
                event.preventDefault();
                
                const tareaId = editarTareaButton.dataset.tareaid;
                const tareaDescripcion = editarTareaButton.dataset.tareadescripcion;
                const tareaIdProyecto = editarTareaButton.dataset.tareaidproyecto;
                const tareaIdProyectoModulo = editarTareaButton.dataset.tareaidproyectomodulo;
                const tareaNombreProyectoModulo = editarTareaButton.dataset.tareanombreproyectomodulo;
                const tareaIdHistoria = editarTareaButton.dataset.tareaidhistoria;
                const tareaNombreHistoria = editarTareaButton.dataset.tareanombrehistoria;
                const tareaNota = editarTareaButton.dataset.tareanota;
                
                
                
                // Populate the modal form
                document.getElementById('editar-tarea-id').value = tareaId;
                document.getElementById('editar-tarea-descripcion').value = tareaDescripcion;
                document.getElementById('editar-tarea-id-proyecto').value = tareaIdProyecto;
                document.getElementById('editar-modulo-hidden').value = tareaIdProyectoModulo || '';
                document.getElementById('editar-modulo-autocomplete').value = tareaNombreProyectoModulo || '';
                document.getElementById('editar-tarea-nota').value = tareaNota || '';
                // Populate Historia dropdown and set selected value
                populateHistoriaDropdown(tareaIdProyecto, tareaIdHistoria);
                
                // Handle Sprint association
                setupSprintAssociation(tareaId, editarTareaButton);
                
                // Clear previous errors
                document.getElementById('editar-tarea-error').style.display = 'none';
                document.getElementById('editar-modulo-error').style.display = 'none';
                document.getElementById('editar-historia-error').style.display = 'none';
                document.getElementById('editar-descripcion-error').style.display = 'none';
                
                // Reset form validation state
                const form = document.getElementById('editar-tarea-form');
                form.classList.remove('was-validated');
                const inputs = form.querySelectorAll('.is-invalid');
                inputs.forEach(input => input.classList.remove('is-invalid'));
            }
            
            // --- Handle Ver Nota Click ---
            if (verNotaSpan) {
                event.preventDefault();
                // Use getOrCreateInstance to ensure we use the correct modal instance,
                // preventing issues with the backdrop not being removed.
                const modalInstance = bootstrap.Modal.getOrCreateInstance(document.getElementById('verNotaModal'));
                modalInstance.show(verNotaSpan);
            }
        });
    }
    
    // --- Ver Nota Modal Handler ---
    const verNotaModal = document.getElementById('verNotaModal');
    if (verNotaModal) {
        verNotaModal.addEventListener('show.bs.modal', function (event) {
            const triggerElement = event.relatedTarget;
            const descripcion = triggerElement.getAttribute('data-descripcion');
            const modulo = triggerElement.getAttribute('data-modulo');
            const nota = triggerElement.getAttribute('data-nota');
            
            const modalDescripcion = verNotaModal.querySelector('#modal-ver-nota-descripcion');
            const modalModulo = verNotaModal.querySelector('#modal-ver-nota-modulo');
            const modalNota = verNotaModal.querySelector('#modal-ver-nota-text');
            
            modalDescripcion.textContent = descripcion || 'N/A';
            modalModulo.textContent = modulo || 'N/A';
            modalNota.value = nota || 'No hay notas para esta tarea.';
        });
    }
    
    // Initialize child task visibility (collapsed by default)
    const toggleButtons = document.querySelectorAll('.btn-toggle-hijas');
    toggleButtons.forEach(button => {
        const parentId = button.dataset.parentId;
        const childRows = document.querySelectorAll(`tr.tarea-hija[data-parent-id="${parentId}"]`);
        const icon = button.querySelector('i');
        
        // Start collapsed
        button.classList.add('collapsed');
        icon.className = 'fa fa-chevron-right';
        
        // Hide child rows initially
        childRows.forEach(row => {
            row.style.display = 'none';
        });
    });
    
    // Load and restore expanded states from localStorage
    const savedStates = loadExpandedStatesFromLocalStorage();
    if (savedStates && Object.keys(savedStates).length > 0) {
        restoreExpandedStates(savedStates);
    }
    
    // --- AJAX Action Function ---
    function performAjaxAction(action, tareaId) {
        // Store current expanded state before making the request
        const expandedStates = getExpandedStates();
        
        const formData = new FormData();
        formData.append('action', action);
        formData.append('id', tareaId); // For AJAX requests, use 'id' parameter
        formData.append('is_ajax', '1');
        
        // Add current filter state to maintain context
        if (window.ltareasConfig.filtroEstado) {
            formData.append('estado', window.ltareasConfig.filtroEstado);
        }
        
        fetch('ltareas', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Use different notification methods based on action type
                    if (action === 'marcar_terminada' || action === 'marcar_en_progreso' || action === 'eliminar') {
                        // Use toast notification for status changes and deletions
                        showToastNotification(data.message, 'success');
                    } else {
                        // Use SweetAlert for other operations
                        swal({
                            title: "Éxito",
                            text: data.message,
                            icon: "success",
                            button: {
                                text: "Cerrar",
                                value: true,
                                visible: true,
                                className: "btn-success",
                                closeModal: true
                            }
                        });
                    }
                    
                    // Update UI based on action
                    if (data.action === 'eliminar') {
                        // Remove the task row from the table
                        removeTaskFromUI(data.tarea_id);
                    } else {
                        // Update task status in the UI
                        updateTaskStatusInUI(data.tarea_id, data.updated_tarea);
                    }
                    
                    // Restore expanded states after UI update
                    setTimeout(() => {
                        // Convert old format to new format for compatibility
                        const convertedStates = {};
                        Object.keys(expandedStates).forEach(parentId => {
                            convertedStates[`main_${parentId}`] = expandedStates[parentId];
                        });
                        
                        // Merge with localStorage states to preserve user preferences
                        const savedStates = loadExpandedStatesFromLocalStorage();
                        const mergedStates = { ...savedStates, ...convertedStates };
                        
                        restoreExpandedStates(mergedStates);
                        
                        // Save the merged states back to localStorage
                        saveExpandedStatesToLocalStorage();
                    }, 100);
                    
                } else {
                    // Always use SweetAlert for errors
                    swal({
                        title: "Error",
                        text: data.message || 'Ocurrió un error inesperado.',
                        icon: "error",
                        button: {
                            text: "Cerrar",
                            value: true,
                            visible: true,
                            className: "btn-danger",
                            closeModal: true
                        }
                    });
                }
            })
            .catch(error => {
                console.error('Error in AJAX action:', error);
                swal({
                    title: "Error de comunicación",
                    text: 'Error de red al procesar la acción: ' + error.message,
                    icon: "error",
                    button: {
                        text: "Cerrar",
                        value: true,
                        visible: true,
                        className: "btn-danger",
                        closeModal: true
                    }
                });
            });
    }
    
    
    
    // --- UI Update Functions ---
    function removeTaskFromUI(tareaId) {
        const taskRow = document.querySelector(`tr[data-tarea-id="${tareaId}"]`);
        if (taskRow) {
            // If it's a parent task, also remove its children
            if (taskRow.classList.contains('tarea-padre')) {
                const childRows = document.querySelectorAll(`tr.tarea-hija[data-parent-id="${tareaId}"]`);
                childRows.forEach(childRow => {
                    childRow.remove();
                });
            }
            taskRow.remove();
        }
    }
    
    function updateTaskStatusInUI(tareaId, updatedTarea) {
        if (!updatedTarea) return;
        
        const taskRow = document.querySelector(`tr[data-tarea-id="${tareaId}"]`);
        if (taskRow) {
            // Update status badge - specifically target the badge in the "Estado" column (5th td)
            // This avoids updating the wrong badge in case there are multiple badges in the row
            const statusCell = taskRow.querySelector('td:nth-child(5)'); // Estado column (5th column)
            const statusBadge = statusCell ? statusCell.querySelector('.badge:not(.badge-hijo-count)') : null;
            if (statusBadge) {
                statusBadge.className = `badge ${updatedTarea.bg_color}`;
                statusBadge.textContent = updatedTarea.nombre_estado;
            }
            
            // Update date if it's a completion
            if (updatedTarea.fecha_terminacion) {
                const dateCell = taskRow.querySelector('td:last-child');
                if (dateCell) {
                    // Format the date (assuming it comes in a standard format)
                    const formattedDate = formatDate(updatedTarea.fecha_terminacion);
                    dateCell.textContent = formattedDate;
                }
            }
            
            // Update action buttons by re-including the actions partial
            // For now, we'll reload the page section or update buttons manually
            updateActionButtons(taskRow, updatedTarea);
        }
    }
    
    function updateActionButtons(taskRow, updatedTarea) {
        const actionsCell = taskRow.querySelector('td:first-child');
        if (!actionsCell) return;
        
        // Find and update specific buttons based on new status
        const marcarTerminadaBtn = actionsCell.querySelector('.btn-marcar-terminada');
        const marcarEnProgresoBtn = actionsCell.querySelector('.btn-marcar-en-progreso');
        
        // Enable/disable buttons based on new status instead of hiding/showing
        if (updatedTarea.estado === window.ltareasConfig.tareaEstados.TERMINADO) {
            // Task is now completed - disable both status change buttons
            if (marcarTerminadaBtn) marcarTerminadaBtn.disabled = true;
            if (marcarEnProgresoBtn) marcarEnProgresoBtn.disabled = true;
        } else if (updatedTarea.estado === window.ltareasConfig.tareaEstados.EN_PROGRESO) {
            // Task is now in progress - disable the "mark as in progress" button, enable "mark as completed"
            if (marcarEnProgresoBtn) marcarEnProgresoBtn.disabled = true;
            if (marcarTerminadaBtn) marcarTerminadaBtn.disabled = false;
        } else {
            // Task is in other state - enable both buttons
            if (marcarTerminadaBtn) marcarTerminadaBtn.disabled = false;
            if (marcarEnProgresoBtn) marcarEnProgresoBtn.disabled = false;
        }
    }
    
    function formatDate(dateString) {
        if (!dateString) return '';
        
        try {
            const date = new Date(dateString);
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        } catch (e) {
            return dateString; // Return original if parsing fails
        }
    }
    
    // --- Toast Notification Functions ---
    function showToastNotification(message, type = 'success') {
        const toastContainer = document.getElementById('toast-container');
        if (!toastContainer) return;
        
        const toastId = 'toast-' + Date.now();
        const toastHtml = `
			<div class="toast toast-${type}" id="${toastId}" role="alert" aria-live="assertive" aria-atomic="true" data-bs-autohide="true" data-bs-delay="4000">
				<div class="toast-header">
					<i class="fa ${type === 'success' ? 'fa-check-circle text-success' : 'fa-exclamation-circle text-danger'} me-2"></i>
					<strong class="me-auto">${type === 'success' ? 'Éxito' : 'Error'}</strong>
					<button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
				</div>
				<div class="toast-body">
					${message}
				</div>
			</div>
		`;
        
        toastContainer.insertAdjacentHTML('beforeend', toastHtml);
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement);
        toast.show();
        
        // Remove toast element after it's hidden
        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });
    }
    
    // --- Agentes Modal Functions ---
    function loadAgentesDropdown() {
        return fetch('get_agentes_ajax')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    const addSelect = document.getElementById('id_agente');
                    const editSelect = document.getElementById('edit_id_agente');
                    
                    // Clear existing options
                    addSelect.innerHTML = '<option value="">-- Seleccione un agente --</option>';
                    editSelect.innerHTML = '<option value="">-- Seleccione un agente --</option>';
                    
                    // Add agentes to both dropdowns
                    data.agentes.forEach(agente => {
                        const option1 = document.createElement('option');
                        option1.value = agente.id;
                        option1.textContent = agente.descripcion;
                        addSelect.appendChild(option1);
                        
                        const option2 = document.createElement('option');
                        option2.value = agente.id;
                        option2.textContent = agente.descripcion;
                        editSelect.appendChild(option2);
                    });
                } else {
                    throw new Error(data.message || 'Error al cargar agentes');
                }
            });
    }
    
    function loadTareaAgentes(tareaId) {
        return fetch('ltareas', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=get_tarea_agentes&tarea_id=${tareaId}`
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    const agentes = data.agentes;
                    const tableBody = document.getElementById('agentes-table-body');
                    const emptyDiv = document.getElementById('agentes-empty');
                    
                    // Update module information (get from first agent record since it's the same for all)
                    const moduloNombre = agentes && agentes.length > 0 ?
                        (agentes[0].modulo_nombre || 'N/A') : 'N/A';
                    document.getElementById('modal-tarea-modulo').textContent = moduloNombre;
                    
                    // Clear previous content
                    tableBody.innerHTML = '';
                    
                    if (agentes.length === 0) {
                        // Show empty state
                        emptyDiv.style.display = 'block';
                        tableBody.closest('.table-responsive').style.display = 'none';
                    } else {
                        // Hide empty state and show table
                        emptyDiv.style.display = 'none';
                        tableBody.closest('.table-responsive').style.display = 'block';
                        
                        // Populate table with agentes data
                        agentes.forEach(agente => {
                            addAgenteToTable(agente);
                        });
                    }
                } else {
                    throw new Error(data.message || 'Error al cargar los agentes');
                }
            });
    }
    
    function addAgenteToTable(tareaAgente) {
        const tbody = document.getElementById('agentes-table-body');
        if (!tbody) return;
        
        // Remove the "No hay agentes" message if it exists
        const emptyDiv = document.getElementById('agentes-empty');
        if (emptyDiv) {
            emptyDiv.style.display = 'none';
            tbody.closest('.table-responsive').style.display = 'block';
        }
        
        const newRow = tbody.insertRow();
        newRow.dataset.id = tareaAgente.id;
        newRow.innerHTML = `
			<td class="text-center align-middle">
				<button type="button" class="btn btn-xs btn-primary me-1 btn-edit-agente"
				        title="Editar"
				        data-agente-id="${tareaAgente.id}">
					<i class="fa fa-edit"></i>
				</button>
				<button type="button" class="btn btn-xs btn-danger btn-delete-agente"
				        title="Eliminar"
				        data-agente-id="${tareaAgente.id}"
				        data-agente-nombre="${tareaAgente.agente_descripcion || tareaAgente.agente_nombre || 'N/A'}">
					<i class="fa fa-trash-alt"></i>
				</button>
			</td>
			<td class="agente-nombre align-middle">${tareaAgente.agente_descripcion || tareaAgente.agente_nombre || 'N/A'}</td>
			<td class="text-end costo-usd align-middle">$${parseFloat(tareaAgente.costo_usd || 0).toFixed(2)}</td>
			<td class="text-center n-mensajes align-middle">
				<div class="d-flex align-items-center justify-content-center">
					<button type="button" class="btn btn-xs btn-outline-danger me-1 btn-decrement-mensajes"
					        title="Decrementar mensajes"
					        data-agente-id="${tareaAgente.id}"
					        ${(tareaAgente.n_mensajes || 0) <= 0 ? 'disabled' : ''}>
						<i class="fa fa-minus"></i>
					</button>
					<span class="mensajes-count mx-2">${tareaAgente.n_mensajes || 0}</span>
					<button type="button" class="btn btn-xs btn-outline-success btn-increment-mensajes ms-1"
					        title="Incrementar mensajes"
					        data-agente-id="${tareaAgente.id}">
						<i class="fa fa-plus"></i>
					</button>
				</div>
			</td>
		`;
    }
    
    function updateAgenteInTable(tareaAgente) {
        const row = document.querySelector(`tr[data-id="${tareaAgente.id}"]`);
        if (row) {
            row.querySelector('.agente-nombre').textContent = tareaAgente.agente_nombre;
            row.querySelector('.costo-usd').textContent = `$${parseFloat(tareaAgente.costo_usd || 0).toFixed(2)}`;
            
            // Update the message count display
            const mensajesCount = row.querySelector('.mensajes-count');
            if (mensajesCount) {
                mensajesCount.textContent = tareaAgente.n_mensajes || 0;
            }
            
            // Update decrement button state
            const decrementBtn = row.querySelector('.btn-decrement-mensajes');
            if (decrementBtn) {
                decrementBtn.disabled = (tareaAgente.n_mensajes || 0) <= 0;
            }
            
            // Update data attributes for delete button
            const deleteBtn = row.querySelector('.btn-delete-agente');
            if (deleteBtn) {
                deleteBtn.setAttribute('data-agente-nombre', tareaAgente.agente_nombre);
            }
        }
        
        // Update the "Agentes Activos" table after any balance-affecting operation
        updateAgentesActivosTable();
    }
    
    /**
     * Updates the "Agentes Activos" table by fetching fresh data from the server
     */
    function updateAgentesActivosTable() {
        // Only update if the agentes activos panel exists
        const agentesPanel = document.getElementById('agentes-panel');
        if (!agentesPanel) {
            return;
        }
        
        fetch('lagentes_activos_ajax', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.agentes) {
                    const tbody = agentesPanel.querySelector('tbody');
                    if (tbody) {
                        // Clear existing rows
                        tbody.innerHTML = '';
                        
                        // Add updated rows
                        data.agentes.forEach(agente => {
                            const row = document.createElement('tr');
                            row.innerHTML = `
							<td>${agente.descripcion}</td>
							<td class="text-end">${parseFloat(agente.balance || 0).toFixed(2)}</td>
						`;
                            tbody.appendChild(row);
                        });
                    }
                }
            })
            .catch(error => {
                console.error('Error updating agentes activos table:', error);
            });
    }
    
    function updateAgentesButtonState(tareaId) {
        // Get current count of agentes in the modal table
        const tableBody = document.getElementById('agentes-table-body');
        const agentesCount = tableBody ? tableBody.querySelectorAll('tr').length : 0;
        
        // Find the agentes button for this specific task
        const agentesButton = document.querySelector(`button.btn-ver-agentes[data-tareaid="${tareaId}"]`);
        
        if (agentesButton) {
            // Update button styling
            if (agentesCount > 0) {
                // Has agentes - use btn-info style
                agentesButton.classList.remove('btn-outline-info');
                agentesButton.classList.add('btn-info');
                
                // Update tooltip text with count
                agentesButton.title = `Gestionar Agentes Asociados (${agentesCount})`;
            } else {
                // No agentes - use btn-outline-info style
                agentesButton.classList.remove('btn-info');
                agentesButton.classList.add('btn-outline-info');
                
                // Update tooltip text without count
                agentesButton.title = 'Gestionar Agentes de la Tarea';
            }
        }
    }
    
    // --- Sprint Association Functions ---
    function setupSprintAssociation(tareaId, editarTareaButton) {
        const sprintContainer = document.getElementById('editar-sprint-association-container');
        const sprintCheckbox = document.getElementById('editar-sprint-association');
        const sprintText = document.getElementById('editar-sprint-association-text');
        const sprintHelp = document.getElementById('editar-sprint-current-name');
        
        // Get current task's sprint association from data attributes
        // Handle empty string, "null" string, and actual null values
        let tareaIdSprint = editarTareaButton.dataset.tareaidSprint;
        if (!tareaIdSprint || tareaIdSprint === 'null' || tareaIdSprint === '') {
            tareaIdSprint = null;
        } else {
            tareaIdSprint = parseInt(tareaIdSprint);
        }
        
        // Additional check: if the task is in the Sprint panel, it's definitely associated
        const isInSprintPanel = editarTareaButton.closest('#sprint-table-body') !== null;
        
        // Check if there's an active sprint
        const filtroProyectoId = window.ltareasConfig.filtroProyectoId;
        const requestBody = 'action=get_active_sprint' + (filtroProyectoId ? '&proyecto_id=' + filtroProyectoId : '');
        
        fetch('ltareas', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: requestBody
        })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.has_active_sprint) {
                    // There's an active sprint - show the association control
                    sprintContainer.style.display = 'block';
                    
                    const activeSprint = data.sprint;
                    
                    // Determine if currently associated - use multiple checks for reliability
                    let isCurrentlyAssociated = false;
                    
                    if (isInSprintPanel) {
                        // If task is in Sprint panel, it's definitely associated with the active Sprint
                        isCurrentlyAssociated = true;
                    } else {
                        // For tasks in main panel, check the Sprint ID
                        isCurrentlyAssociated = tareaIdSprint !== null && tareaIdSprint === activeSprint.id;
                    }
                    
                    // Set checkbox state
                    sprintCheckbox.checked = isCurrentlyAssociated;
                    
                    // Update text based on current state
                    if (isCurrentlyAssociated) {
                        sprintText.textContent = 'Desasociar tarea del sprint actual';
                        sprintHelp.textContent = `Actualmente asociada a: ${activeSprint.descripcion}`;
                    } else {
                        sprintText.textContent = 'Asociar tarea al sprint actual';
                        sprintHelp.textContent = `Sprint disponible: ${activeSprint.descripcion}`;
                    }
                    
                    // Store sprint ID for form submission
                    sprintCheckbox.dataset.sprintId = activeSprint.id;
                    sprintCheckbox.dataset.currentlyAssociated = isCurrentlyAssociated ? '1' : '0';
                } else {
                    // No active sprint - hide the association control
                    sprintContainer.style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error checking active sprint:', error);
                sprintContainer.style.display = 'none';
            });
    }
    
    // --- Modal Event Handlers ---
    const addAgenteForm = document.getElementById('add-agente-form');
    const editAgenteForm = document.getElementById('edit-agente-form');
    const agentesTableBody = document.getElementById('agentes-table-body');
    const addAgenteFeedback = document.getElementById('add-agente-feedback');
    const editAgenteError = document.getElementById('edit-agente-error');
    const editAgenteModal = new bootstrap.Modal(document.getElementById('editAgenteModal'));
    
    // Add agent form submission
    if (addAgenteForm) {
        addAgenteForm.addEventListener('submit', function(event) {
            event.preventDefault();
            
            let isValid = true;
            const agenteInput = document.getElementById('id_agente');
            const costoInput = document.getElementById('costo_usd');
            const mensajesInput = document.getElementById('n_mensajes');
            
            // Reset previous validation states
            [agenteInput, costoInput, mensajesInput].forEach(input => {
                if (input) input.classList.remove('is-invalid');
            });
            
            // Validate Agente selection
            if (agenteInput && agenteInput.value.trim() === '') {
                isValid = false;
                agenteInput.classList.add('is-invalid');
            }
            
            if (!isValid) {
                addAgenteFeedback.innerHTML = `<div class="alert alert-danger">Por favor, seleccione un agente.</div>`;
                return;
            }
            
            // Client-side validation passed, proceed with AJAX
            addAgenteFeedback.innerHTML = `<div class="alert alert-info">Asignando agente... <span class="spinner-border spinner-border-sm"></span></div>`;
            
            const formData = new FormData();
            formData.append('action', 'crear');
            formData.append('id_tarea', window.currentTareaId);
            formData.append('id_agente', agenteInput.value);
            formData.append('costo_usd', costoInput.value || 0);
            formData.append('n_mensajes', mensajesInput.value || 0);
            
            fetch('ltarea_agentes_ajax', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Success
                        addAgenteFeedback.innerHTML = `<div class="alert alert-success alert-dismissible fade show" role="alert">
						${data.message}
						<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
					</div>`;
                        addAgenteToTable(data.tarea_agente);
                        addAgenteForm.reset();
                        // Remove any lingering validation classes
                        [agenteInput, costoInput, mensajesInput].forEach(input => {
                            if (input) input.classList.remove('is-invalid');
                        });
                        // Update the agentes button state in the main task list
                        updateAgentesButtonState(window.currentTareaId);
                        // Update the "Agentes Activos" table
                        updateAgentesActivosTable();
                    } else {
                        // Server-side validation or other error
                        addAgenteFeedback.innerHTML = `<div class="alert alert-danger">${data.message || 'Ocurrió un error inesperado.'}</div>`;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    addAgenteFeedback.innerHTML = `<div class="alert alert-danger">Error de comunicación. Inténtelo de nuevo.</div>`;
                });
        });
    }
    
    // Edit agent form submission
    if (editAgenteForm) {
        editAgenteForm.addEventListener('submit', function(event) {
            event.preventDefault();
            
            const formData = new FormData();
            formData.append('action', 'modificar');
            formData.append('id', document.getElementById('edit-tarea-agente-id').value);
            formData.append('id_tarea', window.currentTareaId);
            formData.append('id_agente', document.getElementById('edit_id_agente').value);
            formData.append('costo_usd', document.getElementById('edit_costo_usd').value || 0);
            formData.append('n_mensajes', document.getElementById('edit_n_mensajes').value || 0);
            
            // Hide previous errors
            editAgenteError.style.display = 'none';
            
            fetch('ltarea_agentes_ajax', {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update row in table
                        updateAgenteInTable(data.tarea_agente);
                        // Close modal
                        editAgenteModal.hide();
                        // Update the agentes button state in the main task list
                        updateAgentesButtonState(window.currentTareaId);
                    } else {
                        editAgenteError.textContent = data.message || 'Error al actualizar agente.';
                        editAgenteError.style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    editAgenteError.textContent = 'Error al actualizar agente. Inténtelo de nuevo.';
                    editAgenteError.style.display = 'block';
                });
        });
    }
    
    // Handle edit, delete, and increment/decrement buttons in the agentes table
    if (agentesTableBody) {
        agentesTableBody.addEventListener('click', function(event) {
            const editButton = event.target.closest('.btn-edit-agente');
            const deleteButton = event.target.closest('.btn-delete-agente');
            const incrementButton = event.target.closest('.btn-increment-mensajes');
            const decrementButton = event.target.closest('.btn-decrement-mensajes');
            
            if (editButton) {
                const id = editButton.dataset.agenteId;
                const row = editButton.closest('tr');
                
                if (row) {
                    // Populate edit form
                    document.getElementById('edit-tarea-agente-id').value = id;
                    
                    // Get current values from the row
                    const agenteNombre = row.querySelector('.agente-nombre').textContent;
                    const costoText = row.querySelector('.costo-usd').textContent.replace('$', '');
                    const mensajes = row.querySelector('.mensajes-count').textContent;
                    
                    // Find the agente ID by name in the dropdown
                    const editSelect = document.getElementById('edit_id_agente');
                    for (let option of editSelect.options) {
                        if (option.textContent === agenteNombre) {
                            editSelect.value = option.value;
                            break;
                        }
                    }
                    
                    document.getElementById('edit_costo_usd').value = costoText;
                    document.getElementById('edit_n_mensajes').value = mensajes;
                    
                    // Hide previous errors
                    editAgenteError.style.display = 'none';
                    
                    // Show modal
                    editAgenteModal.show();
                }
            }
            
            if (deleteButton) {
                const id = deleteButton.dataset.agenteId;
                const agenteNombre = deleteButton.dataset.agenteNombre;
                
                swal({
                    title: "Confirmar Eliminación",
                    text: `¿Seguro que quieres eliminar la asignación del agente "${agenteNombre}"?`,
                    icon: "warning",
                    buttons: {
                        cancel: {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
                        confirm: {text: "Eliminar", value: true, visible: true, className: "btn-danger", closeModal: true}
                    },
                    dangerMode: true,
                }).then((willDelete) => {
                    if (willDelete) {
                        const formData = new FormData();
                        formData.append('action', 'eliminar');
                        formData.append('id', id);
                        
                        fetch('ltarea_agentes_ajax', {
                            method: 'POST',
                            body: formData,
                            headers: { 'X-Requested-With': 'XMLHttpRequest' }
                        })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    // Remove row from table
                                    const row = deleteButton.closest('tr');
                                    if (row) {
                                        row.remove();
                                    }
                                    
                                    // Check if table is now empty
                                    const remainingRows = agentesTableBody.querySelectorAll('tr');
                                    if (remainingRows.length === 0) {
                                        document.getElementById('agentes-empty').style.display = 'block';
                                        agentesTableBody.closest('.table-responsive').style.display = 'none';
                                    }
                                    
                                    // Update the agentes button state in the main task list
                                    updateAgentesButtonState(window.currentTareaId);
                                    
                                    // Update the "Agentes Activos" table
                                    updateAgentesActivosTable();
                                    
                                    showToastNotification(data.message, 'success');
                                } else {
                                    swal("Error", data.message || 'Error al eliminar agente.', "error");
                                }
                            })
                            .catch(error => {
                                console.error('Error:', error);
                                swal("Error", 'Error de comunicación al eliminar agente.', "error");
                            });
                    }
                });
            }
            
            // Handle increment button
            if (incrementButton) {
                const id = incrementButton.dataset.agenteId;
                const row = incrementButton.closest('tr');
                
                if (row) {
                    const currentCount = parseInt(row.querySelector('.mensajes-count').textContent) || 0;
                    updateMensajesCount(id, currentCount + 1);
                }
            }
            
            // Handle decrement button
            if (decrementButton) {
                const id = decrementButton.dataset.agenteId;
                const row = decrementButton.closest('tr');
                
                if (row) {
                    const currentCount = parseInt(row.querySelector('.mensajes-count').textContent) || 0;
                    if (currentCount > 0) {
                        updateMensajesCount(id, currentCount - 1);
                    }
                }
            }
        });
    }
    
    // Function to update n_mensajes count via AJAX
    function updateMensajesCount(tareaAgenteId, newCount) {
        // Prevent negative values
        if (newCount < 0) {
            return;
        }
        
        // Get the current TareaAgente data to preserve other fields
        const row = document.querySelector(`tr[data-id="${tareaAgenteId}"]`);
        if (!row) return;
        
        // Get current values from the row
        const agenteNombre = row.querySelector('.agente-nombre').textContent;
        const costoText = row.querySelector('.costo-usd').textContent.replace('$', '');
        
        // Find the agente ID by name in the dropdown (we need this for the update)
        const editSelect = document.getElementById('edit_id_agente');
        let agenteId = null;
        for (let option of editSelect.options) {
            if (option.textContent === agenteNombre) {
                agenteId = option.value;
                break;
            }
        }
        
        if (!agenteId) {
            swal("Error", 'No se pudo determinar el ID del agente.', "error");
            return;
        }
        
        // Create form data for AJAX request
        const formData = new FormData();
        formData.append('action', 'modificar');
        formData.append('id', tareaAgenteId);
        formData.append('id_tarea', window.currentTareaId);
        formData.append('id_agente', agenteId);
        formData.append('costo_usd', costoText || 0);
        formData.append('n_mensajes', newCount);
        formData.append('is_increment_decrement', 'true'); // Flag for balance management
        
        // Send AJAX request
        fetch('ltarea_agentes_ajax', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the UI with the new data
                    updateAgenteInTable(data.tarea_agente);
                    // Show success toast notification
                    showToastNotification('Número de mensajes actualizado correctamente', 'success');
                } else {
                    swal("Error", data.message || 'Error al actualizar el número de mensajes.', "error");
                }
            })
            .catch(error => {
                console.error('Error:', error);
                swal("Error", 'Error de comunicación al actualizar el número de mensajes.', "error");
            });
    }
    
    // --- Edit Task Modal Functionality ---
    const editarTareaModal = new bootstrap.Modal(document.getElementById('editarTareaModal'));
    const editarTareaForm = document.getElementById('editar-tarea-form');
    const editarCreateModuloModal = new bootstrap.Modal(document.getElementById('editarCreateModuloModal'));
    const editarCreateModuloForm = document.getElementById('editar-create-modulo-form');
    
    // Module Autocomplete functionality for edit modal
    const editarModuloAutocomplete = document.getElementById('editar-modulo-autocomplete');
    const editarModuloHidden = document.getElementById('editar-modulo-hidden');
    const editarModuloLoading = document.getElementById('editar-modulo-loading');
    const editarModuloError = document.getElementById('editar-modulo-error');
    const editarProyectoHidden = document.getElementById('editar-tarea-id-proyecto');
    
    if (editarModuloAutocomplete && editarProyectoHidden) {
        let searchTimeout;
        let currentAbortController;
        
        // Initialize jQuery UI autocomplete
        $(editarModuloAutocomplete).autocomplete({
            minLength: 2,
            delay: 300,
            appendTo: '#editarTareaModal', // Append to modal to fix z-index issues
            position: { my: "left top", at: "left bottom", collision: "flip" }, // Better positioning
            source: function(request, response) {
                // Check if project is selected
                if (!editarProyectoHidden.value) {
                    response([]);
                    return;
                }
                
                // Clear previous timeout
                if (searchTimeout) {
                    clearTimeout(searchTimeout);
                }
                
                // Abort previous request
                if (currentAbortController) {
                    currentAbortController.abort();
                }
                
                // Create new AbortController for this request
                currentAbortController = new AbortController();
                
                // Show loading indicator
                editarModuloLoading.style.display = 'block';
                editarModuloError.style.display = 'none';
                
                // Set timeout for the search
                searchTimeout = setTimeout(() => {
                    const formData = new FormData();
                    formData.append('query', request.term);
                    formData.append('id_proyecto', editarProyectoHidden.value);
                    formData.append('limit', '10');
                    
                    fetch('search_modulos_ajax', {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        signal: currentAbortController.signal
                    })
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`HTTP error! status: ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(data => {
                            editarModuloLoading.style.display = 'none';
                            
                            if (data.success) {
                                // Transform results for jQuery UI autocomplete
                                const autocompleteResults = data.results.map(item => ({
                                    label: item.display_text,
                                    value: item.display_text,
                                    id: item.id,
                                    descripcion: item.descripcion
                                }));
                                response(autocompleteResults);
                            } else {
                                editarModuloError.textContent = data.message || 'Error al buscar módulos.';
                                editarModuloError.style.display = 'block';
                                response([]);
                            }
                        })
                        .catch(error => {
                            editarModuloLoading.style.display = 'none';
                            if (error.name !== 'AbortError') {
                                console.error('Error searching modules:', error);
                                editarModuloError.textContent = 'Error al buscar módulos. Inténtelo de nuevo.';
                                editarModuloError.style.display = 'block';
                            }
                            response([]);
                        });
                }, 300);
            },
            select: function(event, ui) {
                // Set the hidden field value
                editarModuloHidden.value = ui.item.id;
                editarModuloError.style.display = 'none';
                return true;
            }
        });
        
        // Clear hidden field when input is manually cleared
        editarModuloAutocomplete.addEventListener('input', function() {
            if (this.value.trim() === '') {
                editarModuloHidden.value = '';
                editarModuloError.style.display = 'none';
            }
        });
        
        // Handle blur event to validate selection
        editarModuloAutocomplete.addEventListener('blur', function() {
            // If there's text but no ID selected, clear the field
            if (this.value.trim() !== '' && editarModuloHidden.value === '') {
                setTimeout(() => {
                    // Only clear if autocomplete menu is not open
                    if (!$(this).autocomplete('widget').is(':visible')) {
                        this.value = '';
                        editarModuloHidden.value = '';
                    }
                }, 200);
            }
        });
    }
    
    
    
    // Handle "New Module" button click
    const editarBtnNuevoModulo = document.getElementById('editar-btn-nuevo-modulo');
    if (editarBtnNuevoModulo) {
        editarBtnNuevoModulo.addEventListener('click', function() {
            // Clear the create module form
            document.getElementById('editar-nuevo-modulo-descripcion').value = '';
            document.getElementById('editar-nuevo-modulo-error').style.display = 'none';
            
            // Show the create module modal
            editarCreateModuloModal.show();
        });
    }
    
    // Handle create module form submission
    if (editarCreateModuloForm) {
        editarCreateModuloForm.addEventListener('submit', function(event) {
            event.preventDefault();
            
            const descripcion = document.getElementById('editar-nuevo-modulo-descripcion').value.trim();
            const id_proyecto = editarProyectoHidden.value;
            
            // Validate form data
            if (!descripcion) {
                document.getElementById('editar-nuevo-modulo-error').textContent = 'La descripción es requerida.';
                document.getElementById('editar-nuevo-modulo-error').style.display = 'block';
                return;
            }
            
            // Hide error message
            document.getElementById('editar-nuevo-modulo-error').style.display = 'none';
            
            // Create form data for AJAX request
            const formData = new FormData();
            formData.append('action', 'crear');
            formData.append('id_proyecto', id_proyecto);
            formData.append('descripcion', descripcion);
            
            // Send AJAX request
            fetch('lproyectos_modulos_ajax', {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Set the values in the edit task form
                        editarModuloAutocomplete.value = descripcion;
                        editarModuloHidden.value = data.id;
                        
                        // Close the create module modal
                        editarCreateModuloModal.hide();
                        
                        // Show success message
                        showToastNotification('Módulo creado correctamente', 'success');
                    } else {
                        document.getElementById('editar-nuevo-modulo-error').textContent = data.message || 'Error al crear el módulo.';
                        document.getElementById('editar-nuevo-modulo-error').style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('editar-nuevo-modulo-error').textContent = 'Error de comunicación al crear el módulo.';
                    document.getElementById('editar-nuevo-modulo-error').style.display = 'block';
                });
        });
    }
    
    // Handle edit task form submission
    if (editarTareaForm) {
        editarTareaForm.addEventListener('submit', function(event) {
            event.preventDefault();
            
            // Get form data
            const tareaId = document.getElementById('editar-tarea-id').value;
            const descripcion = document.getElementById('editar-tarea-descripcion').value.trim();
            const idProyectoModulo = document.getElementById('editar-modulo-hidden').value;
            const idHistoria = document.getElementById('editar-historia-select').value;
            const nota = document.getElementById('editar-tarea-nota').value.trim();
            
            // Client-side validation
            let isValid = true;
            
            // Reset previous validation states
            document.getElementById('editar-tarea-descripcion').classList.remove('is-invalid');
            document.getElementById('editar-descripcion-error').style.display = 'none';
            document.getElementById('editar-historia-select').classList.remove('is-invalid');
            document.getElementById('editar-historia-error').style.display = 'none';
            document.getElementById('editar-tarea-error').style.display = 'none';
            
            // Validate description
            if (!descripcion) {
                document.getElementById('editar-tarea-descripcion').classList.add('is-invalid');
                document.getElementById('editar-descripcion-error').style.display = 'block';
                isValid = false;
            }
            
            // Validate historia (required)
            if (!idHistoria) {
                document.getElementById('editar-historia-select').classList.add('is-invalid');
                document.getElementById('editar-historia-error').textContent = 'La historia es requerida.';
                document.getElementById('editar-historia-error').style.display = 'block';
                isValid = false;
            }
            
            if (!isValid) {
                return;
            }
            
            // Disable submit button during request
            const submitButton = editarTareaForm.querySelector('button[type="submit"]');
            const originalText = submitButton.textContent;
            submitButton.disabled = true;
            submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Actualizando...';
            
            // Handle Sprint association
            const sprintCheckbox = document.getElementById('editar-sprint-association');
            let sprintAction = null;
            let sprintId = null;
            
            if (sprintCheckbox && sprintCheckbox.dataset.sprintId) {
                const isChecked = sprintCheckbox.checked;
                const wasAssociated = sprintCheckbox.dataset.currentlyAssociated === '1';
                sprintId = sprintCheckbox.dataset.sprintId;
                
                if (isChecked && !wasAssociated) {
                    sprintAction = 'associate';
                } else if (!isChecked && wasAssociated) {
                    sprintAction = 'disassociate';
                }
                
                
            }
            
            // Create form data for AJAX request
            const formData = new FormData();
            formData.append('action', 'editar');
            formData.append('id', tareaId);
            formData.append('descripcion', descripcion);
            formData.append('id_proyecto_modulo', idProyectoModulo || '');
            formData.append('id_historia', idHistoria || '');
            formData.append('nota', nota || '');
            formData.append('is_ajax', '1');
            
            // Add Sprint association data if needed
            if (sprintAction && sprintId) {
                formData.append('sprint_action', sprintAction);
                formData.append('sprint_id', sprintId);
            }
            
            // Send AJAX request
            fetch('ltareas', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        // Close the modal
                        editarTareaModal.hide();
                        
                        // Show success message
                        showToastNotification(data.message, 'success');
                        
                        // Check if Sprint association changed - if so, refresh both task lists
                        if (data.sprint_association_changed) {
                            refreshTaskLists();
                        } else {
                            // Update the task in the UI using the old method for non-Sprint changes
                            updateTaskInUI(tareaId, data.updated_tarea);
                        }
                    } else {
                        document.getElementById('editar-tarea-error').textContent = data.message || 'Error al actualizar la tarea.';
                        document.getElementById('editar-tarea-error').style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('editar-tarea-error').textContent = 'Error de comunicación al actualizar la tarea.';
                    document.getElementById('editar-tarea-error').style.display = 'block';
                })
                .finally(() => {
                    // Re-enable submit button
                    submitButton.disabled = false;
                    submitButton.textContent = originalText;
                });
        });
    }
    
    // Function to update task in UI after edit
    function updateTaskInUI(tareaId, updatedTarea) {
        if (!updatedTarea) return;
        
        // Check if Sprint association changed
        const mainTableRow = document.querySelector('#tarea-table-body tr[data-tarea-id="' + tareaId + '"]');
        const sprintTableRow = document.querySelector('#sprint-table-body tr[data-tarea-id="' + tareaId + '"]');
        
        // Determine if task should be in Sprint table based on updated data
        const shouldBeInSprint = updatedTarea.id_sprint && updatedTarea.id_sprint !== null && updatedTarea.id_sprint !== '';
        
        // Handle Sprint association changes
        if (shouldBeInSprint && !sprintTableRow) {
            // Task was associated with Sprint but not in Sprint table - add it dynamically
            addTaskToSprintTable(updatedTarea);
        } else if (!shouldBeInSprint && sprintTableRow) {
            // Task was disassociated from Sprint - remove from Sprint table
            sprintTableRow.remove();
            
            // Check if Sprint table is now empty and show empty message
            checkAndShowSprintEmptyState();
        }
        
        // Update task in main table (always present)
        updateTaskRowInTable(tareaId, updatedTarea, '#tarea-table-body');
        
        // Update task in sprint table if it exists and should be there
        if (shouldBeInSprint) {
            updateTaskRowInTable(tareaId, updatedTarea, '#sprint-table-body');
        }
    }
    
    // Function to add a task to the Sprint table dynamically
    function addTaskToSprintTable(updatedTarea) {
        const sprintTableBody = document.getElementById('sprint-table-body');
        if (!sprintTableBody) return;
        
        // Remove empty state message if it exists
        const emptyRow = sprintTableBody.querySelector('tr td[colspan="6"]');
        if (emptyRow) {
            emptyRow.closest('tr').remove();
        }
        
        // Create the task row HTML
        const taskRow = document.createElement('tr');
        taskRow.setAttribute('data-tarea-id', updatedTarea.id);
        taskRow.className = 'tarea-padre'; // Assuming it's a parent task for now
        
        // Generate action buttons HTML (simplified version)
        const actionsHTML = `
			<button type="button" class="btn btn-xs btn-primary me-1 btn-editar-tarea"
			        title="Editar Tarea"
			        data-bs-toggle="modal"
			        data-bs-target="#editarTareaModal"
			        data-tareaid="${updatedTarea.id}"
			        data-tareadescripcion="${escapeHtml(updatedTarea.descripcion || '')}"
			        data-tareaidproyecto="${updatedTarea.id_proyecto || ''}"
			        data-tareaidproyectomodulo="${updatedTarea.id_proyecto_modulo || ''}"
			        data-tareanombreproyectomodulo="${escapeHtml(updatedTarea.nombre_proyecto_modulo || '')}"
			        data-tareaidhistoria="${updatedTarea.id_historia || ''}"
			        data-tareanombrehistoria="${escapeHtml(updatedTarea.nombre_historia || '')}"
			        data-tareaidSprint="${updatedTarea.id_sprint || ''}"
			        data-tareanota="${escapeHtml(updatedTarea.nota || '')}">
			    <i class="fa fa-edit"></i>
			</button>
			<button type="button" class="btn btn-xs btn-outline-info me-1 btn-ver-agentes"
			        title="Gestionar Agentes de la Tarea"
			        data-bs-toggle="modal"
			        data-bs-target="#gestionarAgentesModal"
			        data-tareaid="${updatedTarea.id}"
			        data-tareadescripcion="${escapeHtml(updatedTarea.descripcion || '')}">
			    <i class="fa fa-users"></i>
			</button>
			<button type="button" class="btn btn-xs btn-success me-1 btn-marcar-terminada"
			        title="Marcar como Terminada"
			        data-tareaid="${updatedTarea.id}">
			    <i class="fa fa-check"></i>
			</button>
			<button type="button" class="btn btn-xs btn-info me-1 btn-marcar-en-progreso"
			        title="Marcar como En Progreso"
			        data-tareaid="${updatedTarea.id}">
			    <i class="fa fa-play"></i>
			</button>
			<button type="button" class="btn btn-xs btn-danger btn-eliminar-tarea"
			        title="Eliminar Tarea"
			        data-tareaid="${updatedTarea.id}">
			    <i class="fa fa-trash-alt"></i>
			</button>
		`;
        
        taskRow.innerHTML = `
			<td class="text-center">${actionsHTML}</td>
			<td class="text-center">${updatedTarea.id}</td>
			<td>${escapeHtml(updatedTarea.nombre_proyecto_modulo || 'N/A')}</td>
			<td><span class="ver-nota" style="cursor: pointer;" data-bs-toggle="modal" data-bs-target="#verNotaModal" data-descripcion="${escapeHtml(updatedTarea.descripcion || 'N/A')}" data-modulo="${escapeHtml(updatedTarea.nombre_proyecto_modulo || 'N/A')}" data-nota="${escapeHtml(updatedTarea.nota || 'N/A')}">${escapeHtml(updatedTarea.descripcion || 'N/A')}${renderNoteIcon(updatedTarea.nota)}</span></td>
			<td class="text-center"><span class="badge ${updatedTarea.bg_color || 'bg-secondary'}">${escapeHtml(updatedTarea.nombre_estado || 'N/A')}</span></td>
			<td class="text-center">${updatedTarea.fecha_terminacion || ''}</td>
		`;
        
        // Add the row to the Sprint table
        sprintTableBody.appendChild(taskRow);
    }
    
    // Function to check if Sprint table is empty and show empty state
    function checkAndShowSprintEmptyState() {
        const sprintTableBody = document.getElementById('sprint-table-body');
        if (!sprintTableBody) return;
        
        const taskRows = sprintTableBody.querySelectorAll('tr[data-tarea-id]');
        if (taskRows.length === 0) {
            // No tasks left, show empty state
            const emptyRow = document.createElement('tr');
            emptyRow.innerHTML = `
				<td colspan="6" class="text-center py-4">
					<i class="fa fa-rocket fa-2x text-muted mb-2"></i>
					<p class="text-muted mb-0">No hay tareas asociadas al sprint activo</p>
				</td>
			`;
            sprintTableBody.appendChild(emptyRow);
        }
    }
    
    // Helper function to escape HTML
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    // Function to refresh both task lists after Sprint association changes
    function refreshTaskLists() {
        // Store current expanded states before refresh
        const expandedStates = getExpandedStates();
        
        // Get current filter parameters
        const filtroProyectoId = window.ltareasConfig.filtroProyectoId;
        const filtroEstado = window.ltareasConfig.filtroEstado;
        
        if (!filtroProyectoId) {
            console.error('No project filter available for refresh');
            return;
        }
        
        // Prepare form data for refresh request
        const formData = new FormData();
        formData.append('action', 'refresh_task_lists');
        formData.append('filtro_proyecto_id', filtroProyectoId);
        if (filtroEstado) {
            formData.append('filtro_estado', filtroEstado);
        }
        formData.append('expanded_states', JSON.stringify(expandedStates));
        formData.append('is_ajax', '1');
        
        // Send AJAX request to refresh both task lists
        fetch('ltareas', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Update main task table body
                    const mainTableBody = document.getElementById('tarea-table-body');
                    if (mainTableBody) {
                        mainTableBody.innerHTML = data.main_table_html;
                    }
                    
                    // Update Sprint task table body
                    const sprintTableBody = document.getElementById('sprint-table-body');
                    if (sprintTableBody) {
                        sprintTableBody.innerHTML = data.sprint_table_html;
                    }
                    
                    // Initialize both panels' child task visibility (collapsed by default) BEFORE restoring states
                    initializeMainPanelToggleButtons();
                    initializeSprintPanelToggleButtons();
                    
                    // Merge server states with localStorage states to preserve user preferences
                    const savedStates = loadExpandedStatesFromLocalStorage();
                    const serverStates = data.expanded_states || expandedStates;
                    const mergedStates = { ...savedStates, ...serverStates };
                    
                    // Restore expanded states (this will override the default collapsed state for expanded parents)
                    restoreExpandedStates(mergedStates);
                    
                    // Save the merged states back to localStorage
                    saveExpandedStatesToLocalStorage();
                } else {
                    console.error('Error refreshing task lists:', data.message);
                    showToastNotification('Error al actualizar las listas de tareas', 'error');
                }
            })
            .catch(error => {
                console.error('Error refreshing task lists:', error);
                showToastNotification('Error de comunicación al actualizar las listas', 'error');
            });
    }
    
    // Function to get current expanded states of parent tasks
    function getExpandedStates() {
        const expandedStates = {};
        
        // Check main table
        const mainToggleButtons = document.querySelectorAll('#tarea-table-body .btn-toggle-hijas');
        mainToggleButtons.forEach(button => {
            const parentId = button.dataset.parentId;
            const isExpanded = !button.classList.contains('collapsed');
            expandedStates[`main_${parentId}`] = isExpanded;
        });
        
        // Check Sprint table
        const sprintToggleButtons = document.querySelectorAll('#sprint-table-body .btn-toggle-hijas');
        sprintToggleButtons.forEach(button => {
            const parentId = button.dataset.parentId;
            const isExpanded = !button.classList.contains('collapsed');
            expandedStates[`sprint_${parentId}`] = isExpanded;
        });
        
        return expandedStates;
    }
    
    // Function to restore expanded states after refresh
    function restoreExpandedStates(expandedStates) {
        if (!expandedStates || typeof expandedStates !== 'object') {
            return;
        }
        
        // Restore main table states
        Object.keys(expandedStates).forEach(key => {
            if (key.startsWith('main_')) {
                const parentId = key.replace('main_', '');
                const isExpanded = expandedStates[key];
                const toggleButton = document.querySelector(`#tarea-table-body .btn-toggle-hijas[data-parent-id="${parentId}"]`);
                
                if (toggleButton && isExpanded) {
                    // Expand this parent's children
                    const childRows = document.querySelectorAll(`#tarea-table-body tr.tarea-hija[data-parent-id="${parentId}"]`);
                    const icon = toggleButton.querySelector('i');
                    
                    toggleButton.classList.remove('collapsed');
                    icon.className = 'fa fa-chevron-down';
                    childRows.forEach(row => {
                        row.style.display = '';
                    });
                }
            } else if (key.startsWith('sprint_')) {
                const parentId = key.replace('sprint_', '');
                const isExpanded = expandedStates[key];
                const toggleButton = document.querySelector(`#sprint-table-body .btn-toggle-hijas[data-parent-id="${parentId}"]`);
                
                if (toggleButton && isExpanded) {
                    // Expand this parent's children
                    const childRows = document.querySelectorAll(`#sprint-table-body tr.tarea-hija[data-parent-id="${parentId}"]`);
                    const icon = toggleButton.querySelector('i');
                    
                    toggleButton.classList.remove('collapsed');
                    icon.className = 'fa fa-chevron-down';
                    childRows.forEach(row => {
                        row.style.display = '';
                    });
                }
            }
        });
    }
    
    // Function to initialize main panel toggle buttons after refresh
    function initializeMainPanelToggleButtons() {
        const mainToggleButtons = document.querySelectorAll('#tarea-table-body .btn-toggle-hijas');
        mainToggleButtons.forEach(button => {
            const parentId = button.dataset.parentId;
            const childRows = document.querySelectorAll(`#tarea-table-body tr.tarea-hija[data-parent-id="${parentId}"]`);
            const icon = button.querySelector('i');
            
            // Set default collapsed state for all buttons (will be overridden by restoreExpandedStates if needed)
            button.classList.add('collapsed');
            icon.className = 'fa fa-chevron-right';
            
            // Hide all child rows by default
            childRows.forEach(row => {
                row.style.display = 'none';
            });
        });
    }
    
    // Function to initialize Sprint panel toggle buttons after refresh
    function initializeSprintPanelToggleButtons() {
        const sprintToggleButtons = document.querySelectorAll('#sprint-table-body .btn-toggle-hijas');
        sprintToggleButtons.forEach(button => {
            const parentId = button.dataset.parentId;
            const childRows = document.querySelectorAll(`#sprint-table-body tr.tarea-hija[data-parent-id="${parentId}"]`);
            const icon = button.querySelector('i');
            
            // Set default collapsed state for all buttons (will be overridden by restoreExpandedStates if needed)
            button.classList.add('collapsed');
            icon.className = 'fa fa-chevron-right';
            
            // Hide all child rows by default
            childRows.forEach(row => {
                row.style.display = 'none';
            });
        });
    }
    
    // ===== EXPANDED STATES PERSISTENCE FUNCTIONS =====
    
    // Get localStorage key for current project
    function getLocalStorageKey() {
        const projectId = window.ltareasConfig.filtroProyectoId;
        return projectId ? `ltareas_expanded_states_${projectId}` : 'ltareas_expanded_states_global';
    }
    
    // Save expanded states to localStorage
    function saveExpandedStatesToLocalStorage() {
        try {
            const states = getExpandedStates();
            const key = getLocalStorageKey();
            localStorage.setItem(key, JSON.stringify(states));
        } catch (e) {
            console.warn('Could not save expanded states to localStorage:', e);
        }
    }
    
    // Load expanded states from localStorage
    function loadExpandedStatesFromLocalStorage() {
        try {
            const key = getLocalStorageKey();
            const stored = localStorage.getItem(key);
            return stored ? JSON.parse(stored) : {};
        } catch (e) {
            console.warn('Could not load expanded states from localStorage:', e);
            return {};
        }
    }
    
    // Helper function to update a task row in a specific table
    function updateTaskRowInTable(tareaId, updatedTarea, tableSelector) {
        const tableBody = document.querySelector(tableSelector);
        if (!tableBody) return;
        
        const taskRow = tableBody.querySelector(`tr[data-tarea-id="${tareaId}"]`);
        if (taskRow) {
            // --- Safely update the description cell to preserve modal functionality ---
            const descriptionCell = taskRow.querySelector('td:nth-child(4)');
            if (descriptionCell) {
                // Find the clickable span element
                const verNotaSpan = descriptionCell.querySelector('.ver-nota');
                
                if (verNotaSpan) {
                    // 1. Update the visible text content of the span
                    // We need to find the text node to replace, to avoid removing other nodes like the chevron icon
                    let textNode = null;
                    for (const node of verNotaSpan.childNodes) {
                        if (node.nodeType === Node.TEXT_NODE && node.textContent.trim().length > 0) {
                            textNode = node;
                            break;
                        }
                    }
                    if (textNode) {
                        textNode.textContent = ' ' + (updatedTarea.descripcion || 'N/A');
                    } else {
                        // Fallback if no suitable text node is found
                        verNotaSpan.innerHTML = escapeHtml(updatedTarea.descripcion || 'N/A') + renderNoteIcon(updatedTarea.nota);
                    }

                    // Update or add note icon after text content
                    const existingIcon = verNotaSpan.querySelector('.fa-circle-exclamation');
                    if (existingIcon) {
                        existingIcon.remove();
                    }
                    if (updatedTarea.nota && updatedTarea.nota.trim() !== '' && updatedTarea.nota !== 'N/A') {
                        verNotaSpan.insertAdjacentHTML('beforeend', renderNoteIcon(updatedTarea.nota));
                    }
                    
                    // 2. Update the data attributes for the modal
                    verNotaSpan.dataset.descripcion = updatedTarea.descripcion || 'N/A';
                    verNotaSpan.dataset.modulo = updatedTarea.nombre_proyecto_modulo || 'N/A';
                    verNotaSpan.dataset.nota = updatedTarea.nota || 'N/A';
                } else {
                    // Fallback for child tasks or tasks without a modal span
                    const isChildTask = taskRow.classList.contains('tarea-hija');
                    if (isChildTask) {
                        const descripcionDiv = descriptionCell.querySelector('.descripcion-hija');
                        if (descripcionDiv) {
                            const childVerNotaSpan = descripcionDiv.querySelector('.ver-nota');
                            if(childVerNotaSpan) {
                                childVerNotaSpan.innerHTML = escapeHtml(updatedTarea.descripcion || 'N/A') + renderNoteIcon(updatedTarea.nota);
                                childVerNotaSpan.dataset.descripcion = updatedTarea.descripcion || 'N/A';
                                childVerNotaSpan.dataset.modulo = updatedTarea.nombre_proyecto_modulo || 'N/A';
                                childVerNotaSpan.dataset.nota = updatedTarea.nota || 'N/A';
                            }
                        }
                    }
                }
            }
            
            // Update module in the module column (3rd td)
            const moduleCell = taskRow.querySelector('td:nth-child(3)');
            if (moduleCell) {
                moduleCell.textContent = updatedTarea.nombre_proyecto_modulo || 'N/A';
            }
            
            // Update the edit button data attributes
            const editButton = taskRow.querySelector('.btn-editar-tarea');
            if (editButton) {
                editButton.dataset.tareadescripcion = updatedTarea.descripcion || '';
                editButton.dataset.tareaidproyectomodulo = updatedTarea.id_proyecto_modulo || '';
                editButton.dataset.tareanombreproyectomodulo = updatedTarea.nombre_proyecto_modulo || '';
                // Update Historia data attributes
                editButton.dataset.tareaidhistoria = updatedTarea.id_historia || '';
                editButton.dataset.tareanombrehistoria = updatedTarea.nombre_historia || '';
                // Update nota data attribute
                editButton.dataset.tareanota = updatedTarea.nota || '';
                // Update sprint association data if available
                if (updatedTarea.id_sprint !== undefined) {
                    editButton.dataset.tareaidSprint = updatedTarea.id_sprint || '';
                }
            }
        }
    }
    
    // --- Sprint Panel JavaScript ---
    
    // Sprint Creation Modal
    const createSprintModal = new bootstrap.Modal(document.getElementById('createSprintModal'));
    const createSprintForm = document.getElementById('create-sprint-form');
    const editSprintFieldModal = new bootstrap.Modal(document.getElementById('editSprintFieldModal'));
    const editSprintFieldForm = document.getElementById('edit-sprint-field-form');
    
    // Create Sprint button handler
    const btnCreateSprint = document.getElementById('btn-create-sprint');
    if (btnCreateSprint) {
        btnCreateSprint.addEventListener('click', function() {
            // Pre-populate description with current date
            const today = new Date();
            const dateStr = today.getFullYear() + '-' +
                String(today.getMonth() + 1).padStart(2, '0') + '-' +
                String(today.getDate()).padStart(2, '0');
            
            document.getElementById('sprint-descripcion').value = `Sprint ${dateStr}`;
            document.getElementById('create-sprint-error').style.display = 'none';
            
            createSprintModal.show();
        });
    }
    
    // Create Sprint form submission
    if (createSprintForm) {
        createSprintForm.addEventListener('submit', function(event) {
            event.preventDefault();
            
            const descripcion = document.getElementById('sprint-descripcion').value.trim();
            const errorDiv = document.getElementById('create-sprint-error');
            
            if (!descripcion) {
                errorDiv.textContent = 'La descripción es requerida';
                errorDiv.style.display = 'block';
                return;
            }
            
            // Disable submit button
            const submitBtn = createSprintForm.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm"></span> Creando...';
            
            // Send AJAX request
            const formData = new FormData();
            formData.append('action', 'create_sprint');
            formData.append('descripcion', descripcion);
            
            // Include project ID if available
            const filtroProyectoId = window.ltareasConfig.filtroProyectoId;
            if (filtroProyectoId) {
                formData.append('proyecto_id', filtroProyectoId);
            }
            
            fetch('ltareas', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Close modal and reload page to show new sprint
                        createSprintModal.hide();
                        showToastNotification(data.message, 'success');
                        
                        // Reload page to update sprint panel
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else {
                        errorDiv.textContent = data.message;
                        errorDiv.style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('Error creating sprint:', error);
                    errorDiv.textContent = 'Error de comunicación al crear el sprint';
                    errorDiv.style.display = 'block';
                })
                .finally(() => {
                    submitBtn.disabled = false;
                    submitBtn.textContent = originalText;
                });
        });
    }
    
    // Function to reattach Sprint button event listeners after DOM manipulation
    function reattachSprintButtonListeners() {
        // Remove existing listeners by cloning and replacing elements
        const btnEditSprintDescripcion = document.getElementById('btn-edit-sprint-descripcion');
        const btnEditSprintCambiosBd = document.getElementById('btn-edit-sprint-cambios-bd');
        const btnEditSprintCambiosResources = document.getElementById('btn-edit-sprint-cambios-resources');
        const btnFinalizeSprint = document.getElementById('btn-finalize-sprint');
        
        // Re-attach event listeners
        if (btnEditSprintDescripcion) {
            btnEditSprintDescripcion.addEventListener('click', function() {
                openSprintFieldEditModal('descripcion', this.dataset.sprintId);
            });
        }
        
        if (btnEditSprintCambiosBd) {
            btnEditSprintCambiosBd.addEventListener('click', function() {
                openSprintFieldEditModal('cambios_bd', this.dataset.sprintId);
            });
        }
        
        if (btnEditSprintCambiosResources) {
            btnEditSprintCambiosResources.addEventListener('click', function() {
                openSprintFieldEditModal('cambios_resources', this.dataset.sprintId);
            });
        }
        
        if (btnFinalizeSprint) {
            btnFinalizeSprint.addEventListener('click', function() {
                const sprintId = this.dataset.sprintId;
                
                swal({
                    title: "Finalizar Sprint",
                    text: "¿Estás seguro de que quieres finalizar este sprint? Esta acción no se puede deshacer.",
                    icon: "warning",
                    buttons: {
                        cancel: {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
                        confirm: {text: "Finalizar Sprint", value: true, visible: true, className: "btn-danger", closeModal: true}
                    },
                    dangerMode: true,
                }).then((willFinalize) => {
                    if (willFinalize) {
                        finalizeSprintAjax(sprintId);
                    }
                });
            });
        }
    }
    
    // Sprint field edit buttons - Initial setup
    const btnEditSprintDescripcion = document.getElementById('btn-edit-sprint-descripcion');
    const btnEditSprintCambiosBd = document.getElementById('btn-edit-sprint-cambios-bd');
    const btnEditSprintCambiosResources = document.getElementById('btn-edit-sprint-cambios-resources');
    const btnFinalizeSprint = document.getElementById('btn-finalize-sprint');
    
    // Initialize Sprint button event listeners
    reattachSprintButtonListeners();
    
    // Sprint field edit modal functions
    function openSprintFieldEditModal(field, sprintId) {
        document.getElementById('edit-sprint-id').value = sprintId;
        document.getElementById('edit-sprint-field').value = field;
        
        // Hide all field containers
        document.getElementById('descripcion-field').style.display = 'none';
        document.getElementById('cambios-bd-field').style.display = 'none';
        document.getElementById('cambios-resources-field').style.display = 'none';
        
        // Show the appropriate field and set modal title
        const modalTitle = document.getElementById('editSprintFieldModalLabel');
        
        if (field === 'descripcion') {
            document.getElementById('descripcion-field').style.display = 'block';
            modalTitle.textContent = 'Editar Descripción del Sprint';
            // Get current value from the sprint panel title - extract only the text content between icon and buttons
            const titleElement = document.querySelector('#sprint-panel .panel-title');
            if (titleElement) {
                // Get the text content and extract just the description part
                const fullText = titleElement.textContent || titleElement.innerText || '';
                const match = fullText.match(/Sprint Activo:\s*(.+?)(?:\s*Descripción|$)/);
                const currentDesc = match ? match[1].trim() : '';
                document.getElementById('edit-sprint-descripcion').value = currentDesc;
            }
            document.getElementById('edit-sprint-field-error').style.display = 'none';
            editSprintFieldModal.show();
        } else if (field === 'cambios_bd') {
            document.getElementById('cambios-bd-field').style.display = 'block';
            modalTitle.textContent = 'Editar Cambios en Base de Datos';
            // Fetch current sprint data to populate the field
            fetchSprintDataAndPopulateField(sprintId, field);
        } else if (field === 'cambios_resources') {
            document.getElementById('cambios-resources-field').style.display = 'block';
            modalTitle.textContent = 'Editar Cambios en Resources';
            // Fetch current sprint data to populate the field
            fetchSprintDataAndPopulateField(sprintId, field);
        }
    }
    
    // Function to populate Historia dropdown based on project ID
    function populateHistoriaDropdown(projectId, selectedHistoriaId = null) {
        const historiaSelect = document.getElementById('editar-historia-select');
        if (!historiaSelect || !projectId) {
            return;
        }
        
        // Clear existing options except the placeholder
        historiaSelect.innerHTML = '<option value="">-- Seleccione una historia --</option>';
        
        // Show loading state
        const loadingOption = document.createElement('option');
        loadingOption.value = '';
        loadingOption.textContent = 'Cargando historias...';
        loadingOption.disabled = true;
        historiaSelect.appendChild(loadingOption);
        historiaSelect.disabled = true;
        
        // Fetch Historia records for the project
        const formData = new FormData();
        formData.append('action', 'get_historias_by_proyecto');
        formData.append('id_proyecto', projectId);
        
        fetch('ltareas', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
            .then(response => response.json())
            .then(data => {
                // Clear loading state
                historiaSelect.innerHTML = '<option value="">-- Seleccione una historia --</option>';
                historiaSelect.disabled = false;
                
                if (data.success && data.historias) {
                    // Populate dropdown with Historia options
                    data.historias.forEach(historia => {
                        const option = document.createElement('option');
                        option.value = historia.id;
                        option.textContent = historia.nombre;
                        // Use loose equality to handle string/number comparison
                        if (selectedHistoriaId && selectedHistoriaId !== '' && String(historia.id) === String(selectedHistoriaId)) {
                            option.selected = true;
                        }
                        historiaSelect.appendChild(option);
                    });
                } else {
                    // Show error or no data message
                    const noDataOption = document.createElement('option');
                    noDataOption.value = '';
                    noDataOption.textContent = 'No hay historias disponibles';
                    noDataOption.disabled = true;
                    historiaSelect.appendChild(noDataOption);
                }
            })
            .catch(error => {
                console.error('Error fetching historias:', error);
                historiaSelect.innerHTML = '<option value="">-- Error al cargar historias --</option>';
                historiaSelect.disabled = false;
            });
    }
    
    // Function to fetch sprint data and populate modal fields
    function fetchSprintDataAndPopulateField(sprintId, field) {
        // Show loading state
        const fieldInput = document.getElementById(`edit-sprint-${field.replace('_', '-')}`);
        if (fieldInput) {
            fieldInput.value = 'Cargando...';
            fieldInput.disabled = true;
        }
        
        // Fetch sprint data
        const filtroProyectoId = window.ltareasConfig.filtroProyectoId;
        const requestBody = 'action=get_active_sprint' + (filtroProyectoId ? '&proyecto_id=' + filtroProyectoId : '');
        
        fetch('ltareas', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: requestBody
        })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.has_active_sprint && data.sprint) {
                    const sprint = data.sprint;
                    if (fieldInput) {
                        // Populate with the actual field value
                        if (field === 'cambios_bd') {
                            fieldInput.value = sprint.cambios_bd || '';
                        } else if (field === 'cambios_resources') {
                            fieldInput.value = sprint.cambios_resources || '';
                        }
                        fieldInput.disabled = false;
                    }
                } else {
                    if (fieldInput) {
                        fieldInput.value = '';
                        fieldInput.disabled = false;
                    }
                }
                document.getElementById('edit-sprint-field-error').style.display = 'none';
                editSprintFieldModal.show();
            })
            .catch(error => {
                console.error('Error fetching sprint data:', error);
                if (fieldInput) {
                    fieldInput.value = '';
                    fieldInput.disabled = false;
                }
                document.getElementById('edit-sprint-field-error').style.display = 'none';
                editSprintFieldModal.show();
            });
    }
    
    // Sprint field edit form submission
    if (editSprintFieldForm) {
        editSprintFieldForm.addEventListener('submit', function(event) {
            event.preventDefault();
            
            const sprintId = document.getElementById('edit-sprint-id').value;
            const field = document.getElementById('edit-sprint-field').value;
            let value = '';
            
            if (field === 'descripcion') {
                value = document.getElementById('edit-sprint-descripcion').value.trim();
            } else if (field === 'cambios_bd') {
                value = document.getElementById('edit-sprint-cambios-bd').value.trim();
            } else if (field === 'cambios_resources') {
                value = document.getElementById('edit-sprint-cambios-resources').value.trim();
            }
            
            // Disable submit button
            const submitBtn = editSprintFieldForm.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm"></span> Actualizando...';
            
            // Send AJAX request
            const formData = new FormData();
            formData.append('action', 'edit_sprint_field');
            formData.append('sprint_id', sprintId);
            formData.append('field', field);
            formData.append('value', value);
            
            fetch('ltareas', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        editSprintFieldModal.hide();
                        showToastNotification(data.message, 'success');
                        
                        // Update UI if it's description field
                        if (field === 'descripcion') {
                            const titleElement = document.querySelector('#sprint-panel .panel-title');
                            if (titleElement) {
                                // Preserve the buttons container but update only the text content
                                const buttonsContainer = titleElement.querySelector('.float-end');
                                const buttonsHTML = buttonsContainer ? buttonsContainer.outerHTML : '';
                                
                                // Update only the text content, preserving the icon and buttons
                                titleElement.innerHTML = `<i class="fa fa-rocket me-2"></i>Sprint Activo: ${value}${buttonsHTML ? ' ' + buttonsHTML : ''}`;
                                
                                // Re-attach event listeners to the buttons since innerHTML replacement removes them
                                reattachSprintButtonListeners();
                            }
                        }
                    } else {
                        const errorDiv = document.getElementById('edit-sprint-field-error');
                        errorDiv.textContent = data.message;
                        errorDiv.style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('Error updating sprint field:', error);
                    const errorDiv = document.getElementById('edit-sprint-field-error');
                    errorDiv.textContent = 'Error de comunicación al actualizar el campo';
                    errorDiv.style.display = 'block';
                })
                .finally(() => {
                    submitBtn.disabled = false;
                    submitBtn.textContent = originalText;
                });
        });
    }
    
    // Finalize sprint AJAX function
    function finalizeSprintAjax(sprintId) {
        const formData = new FormData();
        formData.append('action', 'finalize_sprint');
        formData.append('sprint_id', sprintId);
        
        fetch('ltareas', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    swal({
                        title: "Sprint Finalizado",
                        text: data.message,
                        icon: "success",
                        button: {
                            text: "Cerrar",
                            value: true,
                            visible: true,
                            className: "btn-success",
                            closeModal: true
                        }
                    }).then(() => {
                        // Reload page to update sprint panel
                        window.location.reload();
                    });
                } else {
                    swal({
                        title: "Error",
                        text: data.message,
                        icon: "error",
                        button: {
                            text: "Cerrar",
                            value: true,
                            visible: true,
                            className: "btn-danger",
                            closeModal: true
                        }
                    });
                }
            })
            .catch(error => {
                console.error('Error finalizing sprint:', error);
                swal({
                    title: "Error de Comunicación",
                    text: "Error al finalizar el sprint. Inténtelo de nuevo.",
                    icon: "error",
                    button: {
                        text: "Cerrar",
                        value: true,
                        visible: true,
                        className: "btn-danger",
                        closeModal: true
                    }
                });
            });
    }
    
    // Handle Sprint panel task actions (same as main task list)
    const sprintTableBody = document.getElementById('sprint-table-body');
    if (sprintTableBody) {
        sprintTableBody.addEventListener('click', function(event) {
            // Use the same event handlers as the main task table
            const marcarTerminadaButton = event.target.closest('.btn-marcar-terminada');
            const marcarEnProgresoButton = event.target.closest('.btn-marcar-en-progreso');
            const eliminarButton = event.target.closest('.btn-eliminar-tarea');
            const verAgentesButton = event.target.closest('.btn-ver-agentes');
            const editarTareaButton = event.target.closest('.btn-editar-tarea');
            const toggleButton = event.target.closest('.btn-toggle-hijas');
            const verNotaSpan = event.target.closest('.ver-nota');
            
            // --- Handle Ver Nota Click ---
            if (verNotaSpan) {
                event.preventDefault();
                // Use getOrCreateInstance to ensure we use the correct modal instance,
                // preventing issues with the backdrop not being removed.
                const modalInstance = bootstrap.Modal.getOrCreateInstance(document.getElementById('verNotaModal'));
                modalInstance.show(verNotaSpan);
            }
            
            // Handle all the same actions as main table
            if (marcarTerminadaButton) {
                event.preventDefault();
                const tareaId = marcarTerminadaButton.dataset.tareaid;
                swal({
                    title: "Confirmar Acción",
                    text: "¿Seguro que quieres marcar esta tarea como terminada?",
                    icon: "warning",
                    buttons: {
                        cancel: {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
                        confirm: {text: "Marcar como Terminada", value: true, visible: true, className: "btn-success", closeModal: true}
                    },
                }).then((willComplete) => {
                    if (willComplete) {
                        performAjaxAction('marcar_terminada', tareaId);
                    }
                });
            }
            
            if (marcarEnProgresoButton) {
                event.preventDefault();
                const tareaId = marcarEnProgresoButton.dataset.tareaid;
                swal({
                    title: "Confirmar Acción",
                    text: "¿Seguro que quieres marcar esta tarea como en progreso?",
                    icon: "warning",
                    buttons: {
                        cancel: {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
                        confirm: {text: "Marcar como En Progreso", value: true, visible: true, className: "btn-info", closeModal: true}
                    },
                }).then((willProgress) => {
                    if (willProgress) {
                        performAjaxAction('marcar_en_progreso', tareaId);
                    }
                });
            }
            
            if (eliminarButton) {
                event.preventDefault();
                const tareaId = eliminarButton.dataset.tareaid;
                swal({
                    title: "Confirmar Eliminación",
                    text: "¿Seguro que quieres eliminar esta tarea? Esta acción no se puede deshacer.",
                    icon: "warning",
                    buttons: {
                        cancel: {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
                        confirm: {text: "Eliminar", value: true, visible: true, className: "btn-danger", closeModal: true}
                    },
                    dangerMode: true,
                }).then((willDelete) => {
                    if (willDelete) {
                        performAjaxAction('eliminar', tareaId);
                    }
                });
            }
            
            if (toggleButton) {
                event.preventDefault();
                const parentId = toggleButton.dataset.parentId;
                const childRows = document.querySelectorAll(`#sprint-table-body tr.tarea-hija[data-parent-id="${parentId}"]`);
                const icon = toggleButton.querySelector('i');
                
                let isCollapsed = toggleButton.classList.contains('collapsed');
                
                childRows.forEach(row => {
                    if (isCollapsed) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
                
                if (isCollapsed) {
                    toggleButton.classList.remove('collapsed');
                    icon.className = 'fa fa-chevron-down';
                } else {
                    toggleButton.classList.add('collapsed');
                    icon.className = 'fa fa-chevron-right';
                }
                
                // Save expanded states to localStorage
                saveExpandedStatesToLocalStorage();
            }
            
            if (verAgentesButton) {
                event.preventDefault();
                const tareaId = verAgentesButton.dataset.tareaid;
                const tareaDescripcion = verAgentesButton.dataset.tareadescripcion;
                
                window.currentTareaId = tareaId;
                document.getElementById('modal-tarea-descripcion').textContent = tareaDescripcion;
                
                document.getElementById('agentes-loading').style.display = 'block';
                document.getElementById('agentes-content').style.display = 'none';
                document.getElementById('agentes-error').style.display = 'none';
                
                Promise.all([
                    loadAgentesDropdown(),
                    loadTareaAgentes(tareaId)
                ]).then(() => {
                    document.getElementById('agentes-loading').style.display = 'none';
                    document.getElementById('agentes-content').style.display = 'block';
                }).catch(error => {
                    console.error('Error loading modal data:', error);
                    document.getElementById('agentes-loading').style.display = 'none';
                    document.getElementById('agentes-content').style.display = 'block';
                    document.getElementById('agentes-error').textContent = 'Error al cargar los datos: ' + error.message;
                    document.getElementById('agentes-error').style.display = 'block';
                });
            }
            
            if (editarTareaButton) {
                event.preventDefault();
                const tareaId = editarTareaButton.dataset.tareaid;
                const tareaDescripcion = editarTareaButton.dataset.tareadescripcion;
                const tareaIdProyecto = editarTareaButton.dataset.tareaidproyecto;
                const tareaIdProyectoModulo = editarTareaButton.dataset.tareaidproyectomodulo;
                const tareaNombreProyectoModulo = editarTareaButton.dataset.tareanombreproyectomodulo;
                const tareaIdHistoria = editarTareaButton.dataset.tareaidhistoria;
                const tareaNota = editarTareaButton.dataset.tareanota;
                
                document.getElementById('editar-tarea-id').value = tareaId;
                document.getElementById('editar-tarea-descripcion').value = tareaDescripcion;
                document.getElementById('editar-tarea-id-proyecto').value = tareaIdProyecto;
                document.getElementById('editar-modulo-hidden').value = tareaIdProyectoModulo || '';
                document.getElementById('editar-modulo-autocomplete').value = tareaNombreProyectoModulo || '';
                document.getElementById('editar-tarea-nota').value = tareaNota || '';
                
                populateHistoriaDropdown(tareaIdProyecto, tareaIdHistoria);
                
                setupSprintAssociation(tareaId, editarTareaButton);
                
                document.getElementById('editar-tarea-error').style.display = 'none';
                document.getElementById('editar-modulo-error').style.display = 'none';
                document.getElementById('editar-descripcion-error').style.display = 'none';
                document.getElementById('editar-historia-error').style.display = 'none';
                
                const form = document.getElementById('editar-tarea-form');
                form.classList.remove('was-validated');
                const inputs = form.querySelectorAll('.is-invalid');
                inputs.forEach(input => input.classList.remove('is-invalid'));
            }
        });
    }
    
    // Note: Sprint panel initialization removed - it was duplicating the main initialization
    // and running after localStorage restoration, causing expanded states to be lost
    
});

// Global function to set project session and navigate to historias
function setProjectSessionAndNavigate(projectId) {
    // Set project ID in session via AJAX and wait for completion
    fetch('ltareas', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=set_project_session&proyecto_id=' + projectId + '&is_ajax=1'
    })
        .then(response => {
            // Navigate to historias page after AJAX call completes
            window.location.href = 'listado-historias';
        })
        .catch(error => {
            console.error('Error setting project session:', error);
            // Navigate anyway in case of error
            window.location.href = 'listado-historias';
        });
}
