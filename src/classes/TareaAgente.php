<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

/**
 * Represents the relationship between a Tarea and an Agente with associated cost and message count.
 */
class TareaAgente
{
	// --- Atributos ---
	private ?int    $id         = null;
	private ?int    $id_tarea   = null;
	private ?int    $id_agente  = null;
	private ?float  $costo_usd  = null;
	private ?int    $n_mensajes = null;
	private ?string $agente_descripcion = null; // Agent description for optimized queries
	private ?float  $agente_balance = null; // Agent balance for optimized queries

	/**
	 * Constructor: Inicializa las propiedades del objeto TareaAgente.
	 */
	public function __construct()
	{
		$this->id         = null;
		$this->id_tarea   = null;
		$this->id_agente  = null;
		$this->costo_usd  = null;
		$this->n_mensajes = 0; // Valor por defecto según la tabla
		$this->agente_descripcion = null;
		$this->agente_balance = null;
	}

	/**
	 * Método estático para construir un objeto TareaAgente desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos de la relación tarea-agente.
	 *
	 * @return self Instancia de TareaAgente.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto             = new self();
			$objeto->id         = isset($resultado['id']) ? (int)$resultado['id'] : null;
			$objeto->id_tarea   = isset($resultado['id_tarea']) ? (int)$resultado['id_tarea'] : null;
			$objeto->id_agente  = isset($resultado['id_agente']) ? (int)$resultado['id_agente'] : null;
			$objeto->costo_usd  = isset($resultado['costo_usd']) ? (float)$resultado['costo_usd'] : null;
			$objeto->n_mensajes = isset($resultado['n_mensajes']) ? (int)$resultado['n_mensajes'] : 0;
			$objeto->agente_descripcion = $resultado['agente_descripcion'] ?? null;
			$objeto->agente_balance = isset($resultado['agente_balance']) ? (float)$resultado['agente_balance'] : null;

			return $objeto;
		} catch (Exception $e) {
			throw new Exception("Error al construir TareaAgente: " . $e->getMessage());
		}
	}

	/**
	 * Valida los datos del objeto antes de guardar.
	 *
	 * @throws Exception Si los datos requeridos están vacíos o son inválidos.
	 */
	private function validar_data(): void
	{
		if ($this->id_tarea === null || $this->id_tarea <= 0) {
			throw new Exception("El ID de la tarea es requerido y debe ser válido.");
		}

		if ($this->id_agente === null || $this->id_agente <= 0) {
			throw new Exception("El ID del agente es requerido y debe ser válido.");
		}

		if ($this->costo_usd !== null && $this->costo_usd < 0) {
			throw new Exception("El costo en USD no puede ser negativo.");
		}

		if ($this->n_mensajes !== null && $this->n_mensajes < 0) {
			throw new Exception("El número de mensajes no puede ser negativo.");
		}
	}

	// --- Métodos de Acceso a Datos ---

	/**
	 * Obtiene una relación tarea-agente por su ID.
	 *
	 * @param int $id       ID de la relación tarea-agente.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto TareaAgente o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			$query = <<<SQL
            SELECT
            	*
            FROM tareas_agentes
            WHERE
            	id = :id
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener TareaAgente (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de relaciones tarea-agente con filtros opcionales.
	 *
	 * @param array $parametros Array con filtros opcionales (id_tarea, id_agente).
	 * @param PDO   $conexion   Conexión PDO.
	 *
	 * @return array Array de objetos TareaAgente.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(array $parametros, PDO $conexion): array
	{
		try {
			$id_tarea  = $parametros['id_tarea'] ?? null;
			$id_agente = $parametros['id_agente'] ?? null;

			$query = <<<SQL
            SELECT
            	*
            FROM tareas_agentes
            SQL;

			$params = [];
			$where  = [];

			// Agregar filtros si se proporcionan
			if ($id_tarea !== null) {
				$where[]              = "id_tarea = :id_tarea";
				$params[':id_tarea']  = $id_tarea;
			}

			if ($id_agente !== null) {
				$where[]               = "id_agente = :id_agente";
				$params[':id_agente']  = $id_agente;
			}

			// Construir la cláusula WHERE si hay filtros
			if (!empty($where)) {
				$query .= " WHERE " . implode(" AND ", $where);
			}

			// Ordenar por ID descendente
			$query .= " ORDER BY id DESC";

			$statement = $conexion->prepare($query);

			// Bind de parámetros si existen
			foreach ($params as $param => $value) {
				$statement->bindValue($param, $value);
			}

			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de TareaAgente: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de relaciones tarea-agente con descripciones de agentes usando JOIN.
	 * Este método es más eficiente que get_list() cuando se necesitan las descripciones de los agentes.
	 *
	 * @param array $parametros Array con filtros opcionales (id_tarea, id_agente).
	 * @param PDO   $conexion   Conexión PDO.
	 *
	 * @return array Array de objetos TareaAgente con agente_descripcion poblado.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list_with_agent_descriptions(array $parametros, PDO $conexion): array
	{
		try {
			$id_tarea  = $parametros['id_tarea'] ?? null;
			$id_agente = $parametros['id_agente'] ?? null;

			$query = <<<SQL
            SELECT
            	ta.*,
            	a.descripcion AS agente_descripcion
            FROM tareas_agentes ta
            INNER JOIN agentes a ON ta.id_agente = a.id
            SQL;

			$params = [];
			$where  = [];

			// Agregar filtros si se proporcionan
			if ($id_tarea !== null) {
				$where[]              = "ta.id_tarea = :id_tarea";
				$params[':id_tarea']  = $id_tarea;
			}

			if ($id_agente !== null) {
				$where[]               = "ta.id_agente = :id_agente";
				$params[':id_agente']  = $id_agente;
			}

			// Construir la cláusula WHERE si hay filtros
			if (!empty($where)) {
				$query .= " WHERE " . implode(" AND ", $where);
			}

			// Ordenar por ID descendente
			$query .= " ORDER BY ta.id DESC";

			$statement = $conexion->prepare($query);

			// Bind de parámetros si existen
			foreach ($params as $param => $value) {
				$statement->bindValue($param, $value);
			}

			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de TareaAgente con descripciones: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de relaciones tarea-agente con descripciones y balance de agentes usando JOIN.
	 * Este método es más eficiente que get_list() cuando se necesitan las descripciones y balance de los agentes.
	 *
	 * @param array $parametros Array con filtros opcionales (id_tarea, id_agente).
	 * @param PDO   $conexion   Conexión PDO.
	 *
	 * @return array Array de objetos TareaAgente con agente_descripcion y balance poblados.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list_with_agent_descriptions_and_balance(array $parametros, PDO $conexion): array
	{
		try {
			$id_tarea  = $parametros['id_tarea'] ?? null;
			$id_agente = $parametros['id_agente'] ?? null;

			$query = <<<SQL
            SELECT
            	ta.*,
            	a.descripcion AS agente_descripcion,
            	a.balance AS agente_balance
            FROM tareas_agentes ta
            INNER JOIN agentes a ON ta.id_agente = a.id
            SQL;

			$params = [];
			$where  = [];

			// Agregar filtros si se proporcionan
			if ($id_tarea !== null) {
				$where[]              = "ta.id_tarea = :id_tarea";
				$params[':id_tarea']  = $id_tarea;
			}

			if ($id_agente !== null) {
				$where[]               = "ta.id_agente = :id_agente";
				$params[':id_agente']  = $id_agente;
			}

			// Construir la cláusula WHERE si hay filtros
			if (!empty($where)) {
				$query .= " WHERE " . implode(" AND ", $where);
			}

			// Ordenar por ID descendente
			$query .= " ORDER BY ta.id DESC";

			$statement = $conexion->prepare($query);

			// Bind de parámetros si existen
			foreach ($params as $param => $value) {
				$statement->bindValue($param, $value);
			}

			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de TareaAgente con descripciones y balance: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene todas las relaciones de agentes para una tarea específica.
	 *
	 * @param int $id_tarea ID de la tarea.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos TareaAgente.
	 * @throws Exception Si hay error en DB.
	 */
	public static function getByTareaId(int $id_tarea, PDO $conexion): array
	{
		return self::get_list(['id_tarea' => $id_tarea], $conexion);
	}

	/**
	 * Cuenta el número de agentes asociados a una tarea específica.
	 *
	 * @param int $id_tarea ID de la tarea.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int Número de agentes asociados.
	 * @throws Exception Si hay error en DB.
	 */
	public static function countByTareaId(int $id_tarea, PDO $conexion): int
	{
		try {
			$query = <<<SQL
            SELECT COUNT(*) AS total
            FROM tareas_agentes
            WHERE id_tarea = :id_tarea
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_tarea', $id_tarea, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? (int)$resultado['total'] : 0;

		} catch (PDOException $e) {
			throw new Exception("Error al contar TareaAgente para tarea (ID: $id_tarea): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene todas las relaciones de agentes para una tarea específica con descripciones de agentes.
	 * Método optimizado que usa JOIN para evitar consultas N+1.
	 *
	 * @param int $id_tarea ID de la tarea.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos TareaAgente con agente_descripcion poblado.
	 * @throws Exception Si hay error en DB.
	 */
	public static function getByTareaIdWithAgentDescriptions(int $id_tarea, PDO $conexion): array
	{
		return self::get_list_with_agent_descriptions(['id_tarea' => $id_tarea], $conexion);
	}

	/**
	 * Obtiene todas las relaciones de agentes para una tarea específica con descripciones y balance de agentes.
	 * Método optimizado que usa JOIN para evitar consultas N+1 e incluye información de balance.
	 *
	 * @param int $id_tarea ID de la tarea.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos TareaAgente con agente_descripcion y balance poblados.
	 * @throws Exception Si hay error en DB.
	 */
	public static function getByTareaIdWithAgentDescriptionsAndBalance(int $id_tarea, PDO $conexion): array
	{
		return self::get_list_with_agent_descriptions_and_balance(['id_tarea' => $id_tarea], $conexion);
	}

	/**
	 * Obtiene todas las relaciones de tareas para un agente específico.
	 *
	 * @param int $id_agente ID del agente.
	 * @param PDO $conexion  Conexión PDO.
	 *
	 * @return array Array de objetos TareaAgente.
	 * @throws Exception Si hay error en DB.
	 */
	public static function getByAgenteId(int $id_agente, PDO $conexion): array
	{
		return self::get_list(['id_agente' => $id_agente], $conexion);
	}

	/**
	 * Obtiene una relación específica entre una tarea y un agente.
	 *
	 * @param int $id_tarea  ID de la tarea.
	 * @param int $id_agente ID del agente.
	 * @param PDO $conexion  Conexión PDO.
	 *
	 * @return self|null Objeto TareaAgente o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function getByTareaAndAgente(int $id_tarea, int $id_agente, PDO $conexion): ?self
	{
		try {
			$query = <<<SQL
            SELECT
            	*
            FROM tareas_agentes
            WHERE
            	id_tarea = :id_tarea
            	AND id_agente = :id_agente
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id_tarea", $id_tarea, PDO::PARAM_INT);
			$statement->bindValue(":id_agente", $id_agente, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener TareaAgente (Tarea: $id_tarea, Agente: $id_agente): " . $e->getMessage());
		}
	}

	/**
	 * Crea una nueva relación tarea-agente en la base de datos.
	 * Automatically updates the associated agent's balance.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID de la nueva relación creada o false en caso de error.
	 * @throws Exception Si los datos requeridos están vacíos o hay error en DB.
	 */
	public function crear(PDO $conexion): int|false
	{
		try {
			$this->validar_data();

			// Start transaction for balance management
			$conexion->beginTransaction();

			$query = <<<SQL
            INSERT INTO tareas_agentes (
            	 id_tarea
            	,id_agente
            	,costo_usd
            	,n_mensajes
            ) VALUES (
            	 :id_tarea
            	,:id_agente
            	,:costo_usd
            	,:n_mensajes
            )
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_tarea', $this->id_tarea, PDO::PARAM_INT);
			$statement->bindValue(':id_agente', $this->id_agente, PDO::PARAM_INT);
			$statement->bindValue(':costo_usd', $this->costo_usd, PDO::PARAM_STR);
			$statement->bindValue(':n_mensajes', $this->n_mensajes, PDO::PARAM_INT);

			$success = $statement->execute();

			if ($success) {
				$this->id = (int)$conexion->lastInsertId();

				// Update agent balance: subtract cost from balance
				$costImpact = Agente::calculateCostImpact($this->n_mensajes ?? 0, $this->costo_usd ?? 0.0);
				if ($costImpact > 0) {
					$agente = Agente::get($this->id_agente, $conexion);
					if ($agente) {
						if (!$agente->subtractFromBalance($costImpact, $conexion)) {
							throw new Exception("Error al actualizar el balance del agente.");
						}
					}
				}

				$conexion->commit();
				return $this->id;
			} else {
				$conexion->rollBack();
				return false;
			}

		} catch (PDOException $e) {
			$conexion->rollBack();
			throw new Exception("Error de base de datos al crear TareaAgente: " . $e->getMessage());
		} catch (Exception $e) {
			$conexion->rollBack();
			throw $e;
		}
	}

	/**
	 * Modifica una relación tarea-agente existente.
	 * Automatically adjusts the associated agent's balance to prevent double-deduction.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la modificación fue exitosa, False en caso contrario.
	 * @throws Exception Si los datos requeridos están vacíos o hay error en DB.
	 */
	public function modificar(PDO $conexion): bool
	{
		if ($this->id === null || $this->id <= 0) {
			throw new Exception("ID de TareaAgente inválido para modificar.");
		}

		try {
			$this->validar_data();

			// Start transaction for balance management
			$conexion->beginTransaction();

			// Get original values to calculate balance adjustment
			$originalTareaAgente = self::get($this->id, $conexion);
			if (!$originalTareaAgente) {
				throw new Exception("No se encontró la TareaAgente original para calcular ajuste de balance.");
			}

			// Calculate original and new cost impacts
			$originalCostImpact = Agente::calculateCostImpact(
				$originalTareaAgente->getNMensajes() ?? 0,
				$originalTareaAgente->getCostoUsd() ?? 0.0
			);
			$newCostImpact = Agente::calculateCostImpact(
				$this->n_mensajes ?? 0,
				$this->costo_usd ?? 0.0
			);

			$query = <<<SQL
            UPDATE tareas_agentes SET
            	 id_tarea = :id_tarea
            	,id_agente = :id_agente
            	,costo_usd = :costo_usd
            	,n_mensajes = :n_mensajes
            WHERE
            	id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_tarea', $this->id_tarea, PDO::PARAM_INT);
			$statement->bindValue(':id_agente', $this->id_agente, PDO::PARAM_INT);
			$statement->bindValue(':costo_usd', $this->costo_usd, PDO::PARAM_STR);
			$statement->bindValue(':n_mensajes', $this->n_mensajes, PDO::PARAM_INT);
			$statement->bindValue(':id', $this->id, PDO::PARAM_INT);

			$success = $statement->execute();

			if ($success) {
				// Handle balance adjustments for agent changes
				if ($originalTareaAgente->getIdAgente() !== $this->id_agente) {
					// Agent changed: restore balance to original agent, deduct from new agent
					if ($originalCostImpact > 0) {
						$originalAgente = Agente::get($originalTareaAgente->getIdAgente(), $conexion);
						if ($originalAgente) {
							$originalAgente->addToBalance($originalCostImpact, $conexion);
						}
					}

					if ($newCostImpact > 0) {
						$newAgente = Agente::get($this->id_agente, $conexion);
						if ($newAgente) {
							$newAgente->subtractFromBalance($newCostImpact, $conexion);
						}
					}
				} else {
					// Same agent: adjust balance by the difference
					$balanceDifference = $newCostImpact - $originalCostImpact;
					if ($balanceDifference != 0) {
						$agente = Agente::get($this->id_agente, $conexion);
						if ($agente) {
							// Subtract the difference (positive difference means more cost, negative means less cost)
							$agente->updateBalance(-$balanceDifference, $conexion);
						}
					}
				}

				$conexion->commit();
				return true;
			} else {
				$conexion->rollBack();
				return false;
			}

		} catch (PDOException $e) {
			$conexion->rollBack();
			throw new Exception("Error de base de datos al modificar TareaAgente (ID: {$this->id}): " . $e->getMessage());
		} catch (Exception $e) {
			$conexion->rollBack();
			throw $e;
		}
	}

	/**
	 * Guarda (inserta o actualiza) la relación tarea-agente.
	 * Determina automáticamente si insertar o actualizar basándose en el ID.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la operación fue exitosa, False en caso contrario.
	 * @throws Exception Si los datos requeridos están vacíos o hay error en DB.
	 */
	public function guardar(PDO $conexion): bool
	{
		if ($this->getId() !== null && $this->getId() > 0) {
			// ID existe, realizar actualización
			return $this->modificar($conexion);
		} else {
			// No hay ID, realizar inserción
			$newId = $this->crear($conexion);
			return $newId !== false;
		}
	}

	/**
	 * Elimina una relación tarea-agente por su ID.
	 * Automatically restores the associated agent's balance.
	 *
	 * @param int $id       ID de la relación a eliminar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la eliminación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function eliminar(int $id, PDO $conexion): bool
	{
		try {
			// Start transaction for balance management
			$conexion->beginTransaction();

			// Get the TareaAgente before deletion to restore balance
			$tareaAgente = self::get($id, $conexion);
			if (!$tareaAgente) {
				throw new Exception("No se encontró la TareaAgente con ID $id para eliminar.");
			}

			// Calculate cost impact to restore to agent balance
			$costImpact = Agente::calculateCostImpact(
				$tareaAgente->getNMensajes() ?? 0,
				$tareaAgente->getCostoUsd() ?? 0.0
			);

			$query = <<<SQL
            DELETE FROM tareas_agentes
            WHERE id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			$success = $statement->execute();

			if ($success) {
				// Restore balance to agent (add back the cost that was previously deducted)
				if ($costImpact > 0) {
					$agente = Agente::get($tareaAgente->getIdAgente(), $conexion);
					if ($agente) {
						if (!$agente->addToBalance($costImpact, $conexion)) {
							throw new Exception("Error al restaurar el balance del agente.");
						}
					}
				}

				$conexion->commit();
				return true;
			} else {
				$conexion->rollBack();
				return false;
			}

		} catch (PDOException $e) {
			$conexion->rollBack();
			throw new Exception("Error de base de datos al eliminar TareaAgente (ID: $id): " . $e->getMessage());
		} catch (Exception $e) {
			$conexion->rollBack();
			throw $e;
		}
	}

	/**
	 * Elimina todas las relaciones de agentes para una tarea específica.
	 *
	 * @param int $id_tarea ID de la tarea.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la eliminación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function eliminarPorTarea(int $id_tarea, PDO $conexion): bool
	{
		try {
			$query = <<<SQL
            DELETE FROM tareas_agentes
            WHERE id_tarea = :id_tarea
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_tarea', $id_tarea, PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al eliminar TareaAgente por tarea (ID: $id_tarea): " . $e->getMessage());
		}
	}

	/**
	 * Elimina todas las relaciones de tareas para un agente específico.
	 *
	 * @param int $id_agente ID del agente.
	 * @param PDO $conexion  Conexión PDO.
	 *
	 * @return bool True si la eliminación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function eliminarPorAgente(int $id_agente, PDO $conexion): bool
	{
		try {
			$query = <<<SQL
            DELETE FROM tareas_agentes
            WHERE id_agente = :id_agente
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_agente', $id_agente, PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al eliminar TareaAgente por agente (ID: $id_agente): " . $e->getMessage());
		}
	}

	/**
	 * Calcula el costo total de todos los agentes para una tarea específica.
	 *
	 * @param int $id_tarea ID de la tarea.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return float Costo total en USD.
	 * @throws Exception Si hay error en DB.
	 */
	public static function calcularCostoTotalPorTarea(int $id_tarea, PDO $conexion): float
	{
		try {
			$query = <<<SQL
            SELECT SUM(costo_usd) AS total_costo
            FROM tareas_agentes
            WHERE id_tarea = :id_tarea
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_tarea', $id_tarea, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado && $resultado['total_costo'] !== null ? (float)$resultado['total_costo'] : 0.0;

		} catch (PDOException $e) {
			throw new Exception("Error al calcular costo total para tarea (ID: $id_tarea): " . $e->getMessage());
		}
	}

	/**
	 * Calcula el número total de mensajes de todos los agentes para una tarea específica.
	 *
	 * @param int $id_tarea ID de la tarea.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int Número total de mensajes.
	 * @throws Exception Si hay error en DB.
	 */
	public static function calcularMensajesTotalesPorTarea(int $id_tarea, PDO $conexion): int
	{
		try {
			$query = <<<SQL
            SELECT SUM(n_mensajes) AS total_mensajes
            FROM tareas_agentes
            WHERE id_tarea = :id_tarea
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_tarea', $id_tarea, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado && $resultado['total_mensajes'] !== null ? (int)$resultado['total_mensajes'] : 0;

		} catch (PDOException $e) {
			throw new Exception("Error al calcular mensajes totales para tarea (ID: $id_tarea): " . $e->getMessage());
		}
	}

	// --- Getters y Setters ---

	public function getId(): ?int
	{
		return $this->id;
	}

	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	public function getIdTarea(): ?int
	{
		return $this->id_tarea;
	}

	public function setIdTarea(?int $id_tarea): self
	{
		$this->id_tarea = $id_tarea;
		return $this;
	}

	public function getIdAgente(): ?int
	{
		return $this->id_agente;
	}

	public function setIdAgente(?int $id_agente): self
	{
		$this->id_agente = $id_agente;
		return $this;
	}

	public function getCostoUsd(): ?float
	{
		return $this->costo_usd;
	}

	public function setCostoUsd(?float $costo_usd): self
	{
		$this->costo_usd = $costo_usd;
		return $this;
	}

	public function getNMensajes(): ?int
	{
		return $this->n_mensajes;
	}

	public function setNMensajes(?int $n_mensajes): self
	{
		$this->n_mensajes = $n_mensajes;
		return $this;
	}

	public function getAgenteDescripcion(): ?string
	{
		return $this->agente_descripcion;
	}

	public function setAgenteDescripcion(?string $agente_descripcion): self
	{
		$this->agente_descripcion = $agente_descripcion;
		return $this;
	}

	public function getAgenteBalance(): ?float
	{
		return $this->agente_balance;
	}

	public function setAgenteBalance(?float $agente_balance): self
	{
		$this->agente_balance = $agente_balance;
		return $this;
	}

	// --- Balance Management Methods ---

	/**
	 * Increments or decrements the number of messages and updates agent balance accordingly.
	 * This method is specifically designed for the +/- button functionality.
	 *
	 * @param int $increment Amount to increment (positive) or decrement (negative).
	 * @param PDO $conexion Database connection.
	 *
	 * @return bool True if the operation was successful, False otherwise.
	 * @throws Exception If there's an error during the operation.
	 */
	public function adjustMensajesWithBalance(int $increment, PDO $conexion): bool
	{
		if ($this->id === null || $this->id <= 0) {
			throw new Exception("ID de TareaAgente inválido para ajustar mensajes.");
		}

		try {
			// Start transaction for balance management
			$conexion->beginTransaction();

			// Calculate new message count
			$newMensajes = max(0, ($this->n_mensajes ?? 0) + $increment);
			$actualIncrement = $newMensajes - ($this->n_mensajes ?? 0);

			// Update the message count
			$this->setNMensajes($newMensajes);

			// Update database
			$query = <<<SQL
            UPDATE tareas_agentes SET
            	n_mensajes = :n_mensajes
            WHERE
            	id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':n_mensajes', $this->n_mensajes, PDO::PARAM_INT);
			$statement->bindValue(':id', $this->id, PDO::PARAM_INT);

			$success = $statement->execute();

			if ($success && $actualIncrement != 0) {
				// Update agent balance: for increment/decrement, only adjust by the increment amount
				// since we're only changing n_mensajes, not costo_usd
				$costImpact = (float)$actualIncrement; // Only the increment amount affects balance
				if ($costImpact != 0) {
					$agente = Agente::get($this->id_agente, $conexion);
					if ($agente) {
						// Subtract the cost impact (positive increment = subtract, negative increment = add back)
						if (!$agente->updateBalance(-$costImpact, $conexion)) {
							throw new Exception("Error al actualizar el balance del agente.");
						}
					}
				}

				$conexion->commit();
				return true;
			} else {
				$conexion->rollBack();
				return $success;
			}

		} catch (PDOException $e) {
			$conexion->rollBack();
			throw new Exception("Error de base de datos al ajustar mensajes (ID: {$this->id}): " . $e->getMessage());
		} catch (Exception $e) {
			$conexion->rollBack();
			throw $e;
		}
	}
}
