<?php

declare(strict_types=1);

use App\classes\Tarea;
use App\classes\Proyecto;
use App\classes\ProyectoModulo;
use App\classes\TareaAgente;
use App\classes\Sprint;
use App\classes\Agente;
use App\classes\Historia;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en ltareas.php.");
	die('Error crítico: No se pudo conectar a la base de datos.');
}

#region region init variables
$tareas = []; // Initialize as an empty array
$filtro_estado = filter_input(INPUT_GET, 'estado', FILTER_VALIDATE_INT);
$clear_project_filter = filter_input(INPUT_GET, 'clear_project', FILTER_VALIDATE_BOOLEAN);
$success_text = '';
$success_display = 'none';
$error_text = '';
$error_display = 'none';
#endregion init variables

#region region Handle Flash Message Success
// Check for a success flash message from session
if (!empty($_SESSION['flash_message_success'])) {
	$success_text = $_SESSION['flash_message_success'];
	$success_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_success']);
}
#endregion Handle Flash Message Success

#region region Handle Flash Message Error
// Check for an error flash message from session
if (!empty($_SESSION['flash_message_error'])) {
	$error_text = $_SESSION['flash_message_error'];
	$error_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_error']);
}
#endregion Handle Flash Message Error

#region region Handle POST Actions (Mark as Complete, Delete, Get Agentes, Sprint Operations)
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
	$action = $_POST['action'];

	// Handle Sprint AJAX operations
	if (in_array($action, ['get_active_sprint', 'create_sprint', 'edit_sprint_field', 'finalize_sprint'])) {
		header('Content-Type: application/json');

		try {
			if ($action === 'get_active_sprint') {
				// Get project ID from POST data if provided
				$proyecto_id = filter_input(INPUT_POST, 'proyecto_id', FILTER_VALIDATE_INT);

				// Use project-specific sprint loading when project ID is provided
				if ($proyecto_id) {
					$activeSprint = Sprint::getActiveSprintByProject($proyecto_id, $conexion);
				} else {
					$activeSprint = Sprint::getActiveSprint($conexion);
				}

				if ($activeSprint) {
					echo json_encode([
						'success' => true,
						'has_active_sprint' => true,
						'sprint' => [
							'id' => $activeSprint->getId(),
							'descripcion' => $activeSprint->getDescripcion(),
							'fecha_inicio' => $activeSprint->getFecha_inicio(),
							'fecha_fin' => $activeSprint->getFecha_fin(),
							'terminado' => $activeSprint->getTerminado(),
							'cambios_bd' => $activeSprint->getCambios_bd(),
							'cambios_resources' => $activeSprint->getCambios_resources(),
							'estado' => $activeSprint->getEstado()
						]
					]);
				} else {
					echo json_encode([
						'success' => true,
						'has_active_sprint' => false,
						'sprint' => null
					]);
				}
				exit;

			} elseif ($action === 'create_sprint') {
				$descripcion_usuario = trim($_POST['descripcion'] ?? '');
				$proyecto_id = filter_input(INPUT_POST, 'proyecto_id', FILTER_VALIDATE_INT);

				if (empty($descripcion_usuario)) {
					echo json_encode(['success' => false, 'message' => 'La descripción es requerida']);
					exit;
				}

				// Check if there's already an active sprint (project-specific if project ID provided)
				if ($proyecto_id) {
					$existingActiveSprint = Sprint::getActiveSprintByProject($proyecto_id, $conexion);
				} else {
					$existingActiveSprint = Sprint::getActiveSprint($conexion);
				}

				if ($existingActiveSprint) {
					$message = $proyecto_id ?
						'Ya existe un sprint activo para este proyecto. Finaliza el sprint actual antes de crear uno nuevo.' :
						'Ya existe un sprint activo. Finaliza el sprint actual antes de crear uno nuevo.';
					echo json_encode(['success' => false, 'message' => $message]);
					exit;
				}

				// Create new sprint
				$sprint = new Sprint();
				$sprint->setFecha_inicio(create_datetime()); // Use timezone-consistent function

				// Set project ID if provided
				if ($proyecto_id) {
					$sprint->setIdProyecto($proyecto_id);
				}

				// Create sprint first to get ID
				$sprint->setDescripcion($descripcion_usuario); // Temporary description
				$newSprintId = $sprint->crear($conexion);

				if ($newSprintId !== false && $newSprintId > 0) {
					// Update with final description format: "ID{Sprint_ID} {User_Description}"
					$finalDescripcion = "ID{$newSprintId} {$descripcion_usuario}";
					$sprint->setId($newSprintId);
					$sprint->setDescripcion($finalDescripcion);
					$sprint->actualizar($conexion);

					echo json_encode([
						'success' => true,
						'message' => 'Sprint creado exitosamente',
						'sprint_id' => $newSprintId,
						'descripcion' => $finalDescripcion
					]);
				} else {
					echo json_encode(['success' => false, 'message' => 'Error al crear el sprint']);
				}
				exit;

			} elseif ($action === 'edit_sprint_field') {
				$sprintId = filter_input(INPUT_POST, 'sprint_id', FILTER_VALIDATE_INT);
				$field = $_POST['field'] ?? '';
				$value = $_POST['value'] ?? '';

				if (!$sprintId || !in_array($field, ['descripcion', 'cambios_bd', 'cambios_resources'])) {
					echo json_encode(['success' => false, 'message' => 'Parámetros inválidos']);
					exit;
				}

				$sprint = Sprint::get_by_id($sprintId, $conexion);
				if (!$sprint) {
					echo json_encode(['success' => false, 'message' => 'Sprint no encontrado']);
					exit;
				}

				// Update the specific field
				if ($field === 'descripcion') {
					$sprint->setDescripcion($value);
				} elseif ($field === 'cambios_bd') {
					$sprint->setCambios_bd($value);
				} elseif ($field === 'cambios_resources') {
					$sprint->setCambios_resources($value);
				}

				$success = $sprint->actualizar($conexion);
				$message = $success ? 'Campo actualizado correctamente' : 'Error al actualizar el campo';

				echo json_encode(['success' => $success, 'message' => $message]);
				exit;

			} elseif ($action === 'finalize_sprint') {
				$sprintId = filter_input(INPUT_POST, 'sprint_id', FILTER_VALIDATE_INT);

				if (!$sprintId) {
					echo json_encode(['success' => false, 'message' => 'ID de sprint inválido']);
					exit;
				}

				$sprint = Sprint::get_by_id($sprintId, $conexion);
				if (!$sprint) {
					echo json_encode(['success' => false, 'message' => 'Sprint no encontrado']);
					exit;
				}

				// Check if sprint can be finalized (all tasks must be finished)
				if (!$sprint->canBeFinalized($conexion)) {
					// Get information about unfinished tasks
					$unfinishedTasks = $sprint->getUnfinishedTasksInfo($conexion);
					$taskCount = count($unfinishedTasks);

					if ($taskCount > 0) {
						$taskList = array_slice($unfinishedTasks, 0, 3); // Show first 3 tasks
						$taskNames = array_map(function($task) {
							return "• " . $task['descripcion'] . " (" . $task['estado_descripcion'] . ")";
						}, $taskList);

						$message = "No se puede finalizar el sprint porque tiene $taskCount tarea(s) sin terminar:\n\n" .
								   implode("\n", $taskNames);

						if ($taskCount > 3) {
							$remaining = $taskCount - 3;
							$message .= "\n... y $remaining tarea(s) más.";
						}

						$message .= "\n\nPor favor, marca todas las tareas como terminadas antes de finalizar el sprint.";
					} else {
						$message = "No se puede finalizar el sprint porque tiene tareas pendientes.";
					}

					echo json_encode(['success' => false, 'message' => $message]);
					exit;
				}

				// All tasks are finished, proceed with sprint finalization
				$sprint->setTerminado(1);
				$sprint->setFecha_fin(create_datetime()); // Use timezone-consistent function

				$success = $sprint->actualizar($conexion);
				$message = $success ? 'Sprint finalizado correctamente' : 'Error al finalizar el sprint';

				echo json_encode(['success' => $success, 'message' => $message]);
				exit;
			}

		} catch (Exception $e) {
			error_log("Error in Sprint AJAX action '$action': " . $e->getMessage());
			echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
			exit;
		}
	}

	// Handle AJAX request for getting Historia records by project
	if ($action === 'get_historias_by_proyecto') {
		header('Content-Type: application/json');

		$proyectoId = filter_input(INPUT_POST, 'id_proyecto', FILTER_VALIDATE_INT);

		if (!$proyectoId) {
			echo json_encode(['success' => false, 'message' => 'ID de proyecto inválido']);
			exit;
		}

		try {
			$historias = Historia::get_by_proyecto_id($proyectoId, $conexion);

			$historiaData = [];
			foreach ($historias as $historia) {
				$historiaData[] = [
					'id' => $historia->getId(),
					'nombre' => $historia->getTitulo()
				];
			}

			echo json_encode([
				'success' => true,
				'historias' => $historiaData
			]);
			exit;

		} catch (Exception $e) {
			error_log("Error fetching historias by proyecto: " . $e->getMessage());
			echo json_encode(['success' => false, 'message' => 'Error al obtener las historias']);
			exit;
		}
	}

	// Handle AJAX request for getting TareaAgente data
	if ($action === 'get_tarea_agentes') {
		header('Content-Type: application/json');

		$tareaId = filter_input(INPUT_POST, 'tarea_id', FILTER_VALIDATE_INT);

		if (!$tareaId) {
			echo json_encode(['success' => false, 'message' => 'ID de tarea inválido']);
			exit;
		}

		try {
			$agentes = TareaAgente::getByTareaIdWithAgentDescriptionsAndBalance($tareaId, $conexion);

			// Get task information to include module data
			$tarea = Tarea::get($tareaId, $conexion);
			$modulo_nombre = $tarea ? $tarea->getNombreProyectoModulo() : 'N/A';

			// Convert objects to arrays for JSON response
			$agentesArray = [];
			foreach ($agentes as $agente) {
				$agentesArray[] = [
					'id' => $agente->getId(),
					'id_tarea' => $agente->getIdTarea(),
					'id_agente' => $agente->getIdAgente(),
					'costo_usd' => $agente->getCostoUsd(),
					'n_mensajes' => $agente->getNMensajes(),
					'agente_descripcion' => $agente->getAgenteDescripcion(),
					'agente_balance' => $agente->getAgenteBalance() ?? 0.0,
					'modulo_nombre' => $modulo_nombre
				];
			}

			echo json_encode(['success' => true, 'agentes' => $agentesArray]);
			exit;

		} catch (Exception $e) {
			error_log("Error fetching TareaAgente data: " . $e->getMessage());
			echo json_encode(['success' => false, 'message' => 'Error al obtener los agentes: ' . $e->getMessage()]);
			exit;
		}
	}

	// Handle AJAX request for setting project session
	if ($action === 'set_project_session') {
		$isAjax = isset($_POST['is_ajax']) && $_POST['is_ajax'] == '1';

		if ($isAjax) {
			$proyecto_id = filter_input(INPUT_POST, 'proyecto_id', FILTER_VALIDATE_INT);

			if ($proyecto_id) {
				$_SESSION['proyecto_id'] = $proyecto_id;
			}
			exit;
		}
	}

	// Check if this is an AJAX request for status changes or deletion
	$isAjax = isset($_POST['is_ajax']) && $_POST['is_ajax'] === '1';

	// Handle AJAX request for refreshing task lists FIRST (doesn't need task ID)
	if ($isAjax && $action === 'refresh_task_lists') {
		header('Content-Type: application/json');

		try {
			// Get current filter parameters
			$filtro_proyecto_id = filter_input(INPUT_POST, 'filtro_proyecto_id', FILTER_VALIDATE_INT);
			$filtro_estado = filter_input(INPUT_POST, 'filtro_estado', FILTER_VALIDATE_INT);
			$expanded_states = $_POST['expanded_states'] ?? [];

			if (!$filtro_proyecto_id) {
				echo json_encode(['success' => false, 'message' => 'ID de proyecto requerido']);
				exit;
			}

			// Build parameters for task query
			$parametros = ['id_proyecto' => $filtro_proyecto_id];
			if ($filtro_estado !== null && $filtro_estado !== '') {
				$parametros['id_tarea_estado'] = $filtro_estado;
			}

			// Get all tasks for the project
			$tareas = Tarea::get_list($parametros, $conexion);

			// Get active sprint for the project
			$active_sprint = Sprint::getActiveSprintByProject($filtro_proyecto_id, $conexion);

			// Separate tasks into Sprint and regular tasks
			$regular_tasks = [];
			$sprint_tasks = [];

			if ($active_sprint) {
				foreach ($tareas as $tarea) {
					$tareaSprintId = $tarea->getIdSprint();
					$activeSprintId = $active_sprint->getId();

					// Debug logging for task separation
					error_log("DEBUG: Task {$tarea->getId()} - Sprint ID: " . ($tareaSprintId ?? 'null') . " (type: " . gettype($tareaSprintId) . "), Active Sprint ID: $activeSprintId (type: " . gettype($activeSprintId) . ")");

					if ($tareaSprintId !== null && $tareaSprintId == $activeSprintId) {
						$sprint_tasks[] = $tarea;
						error_log("DEBUG: Task {$tarea->getId()} assigned to SPRINT");
					} else {
						$regular_tasks[] = $tarea;
						error_log("DEBUG: Task {$tarea->getId()} assigned to REGULAR");
					}
				}
			} else {
				$regular_tasks = $tareas;
			}

			// Get TareaAgente counts for each tarea to determine button states
			// Include counts for both regular tasks and sprint tasks
			$tareas_agentes_counts = [];
			$all_tasks_for_counts = array_merge($regular_tasks, $sprint_tasks);

			foreach ($all_tasks_for_counts as $tarea) {
				try {
					$tareas_agentes_counts[$tarea->getId()] = TareaAgente::countByTareaId($tarea->getId(), $conexion);
				} catch (Exception $e) {
					error_log("Error counting TareaAgente for tarea ID " . $tarea->getId() . ": " . $e->getMessage());
					$tareas_agentes_counts[$tarea->getId()] = 0; // Default to 0 on error
				}
			}

			// Organize tasks hierarchically
			$tareas_organizadas = organizarTareasJerarquicamente($regular_tasks);
			$sprint_tasks_organized = $active_sprint ? organizarTareasJerarquicamente($sprint_tasks) : [];

			// Generate HTML for both task lists
			ob_start();
			include __ROOT__ . '/views/admin/ltareas_main_table_body.php';
			$main_table_html = ob_get_clean();

			ob_start();
			include __ROOT__ . '/views/admin/ltareas_sprint_table_body.php';
			$sprint_table_html = ob_get_clean();

			echo json_encode([
				'success' => true,
				'main_table_html' => $main_table_html,
				'sprint_table_html' => $sprint_table_html,
				'expanded_states' => $expanded_states
			]);

		} catch (Exception $e) {
			error_log("Error refreshing task lists: " . $e->getMessage());
			echo json_encode(['success' => false, 'message' => 'Error al actualizar las listas de tareas']);
		}
		exit;
	}

	// Handle AJAX requests for status changes, deletion, and editing (these need task ID)
	if ($isAjax && in_array($action, ['marcar_terminada', 'marcar_en_progreso', 'eliminar', 'editar'])) {
		header('Content-Type: application/json');

		// For AJAX requests, the task ID is sent as 'id', for non-AJAX as 'tarea_id'
		$tareaId = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT) ?: filter_input(INPUT_POST, 'tarea_id', FILTER_VALIDATE_INT);

		if (!$tareaId) {
			echo json_encode(['success' => false, 'message' => 'ID de tarea inválido']);
			exit;
		}

		try {
			$success = false;
			$message = '';
			$sprintAssociationChanged = false; // Initialize this variable for all actions

			if ($action === 'marcar_terminada') {
				$success = Tarea::marcarTerminada($tareaId, $conexion);
				$message = $success ? "Tarea marcada como terminada correctamente." : "Error: No se pudo marcar la tarea como terminada.";
			} elseif ($action === 'marcar_en_progreso') {
				$success = Tarea::marcarEnProgreso($tareaId, $conexion);
				$message = $success ? "Tarea marcada como en progreso correctamente." : "Error: No se pudo marcar la tarea como en progreso.";
			} elseif ($action === 'eliminar') {
				$success = Tarea::eliminar($tareaId, $conexion);
				$message = $success ? "Tarea eliminada correctamente." : "Error: No se pudo eliminar la tarea.";
			} elseif ($action === 'editar') {
				// Handle task editing
				$descripcion = trim($_POST['descripcion'] ?? '');
				$nota = trim($_POST['nota'] ?? '');

				// Handle id_proyecto_modulo - convert false to null for empty values
				$id_proyecto_modulo_raw = filter_input(INPUT_POST, 'id_proyecto_modulo', FILTER_VALIDATE_INT);
				$id_proyecto_modulo = ($id_proyecto_modulo_raw === false) ? null : $id_proyecto_modulo_raw;

				// Handle id_historia - convert false to null for empty values
				$id_historia_raw = filter_input(INPUT_POST, 'id_historia', FILTER_VALIDATE_INT);
				$id_historia = ($id_historia_raw === false) ? null : $id_historia_raw;

				if (empty($descripcion)) {
					echo json_encode(['success' => false, 'message' => 'La descripción es requerida']);
					exit;
				}

				if (empty($id_historia)) {
					echo json_encode(['success' => false, 'message' => 'La historia es requerida']);
					exit;
				}

				// Get the existing task
				$tarea = Tarea::get($tareaId, $conexion);
				if (!$tarea) {
					echo json_encode(['success' => false, 'message' => 'Tarea no encontrada']);
					exit;
				}

				// Update task properties
				$tarea->setDescripcion($descripcion);
				$tarea->setIdProyectoModulo($id_proyecto_modulo);
				$tarea->setIdHistoria($id_historia);
				$tarea->setNota($nota);

				// Handle Sprint association if provided
				$sprintAction = $_POST['sprint_action'] ?? null;
				$sprintId = filter_input(INPUT_POST, 'sprint_id', FILTER_VALIDATE_INT);



				$sprintAssociationChanged = false;
				if ($sprintAction && $sprintId !== false && $sprintId !== null) {
					if ($sprintAction === 'associate') {
						try {
							// Get all task IDs (parent + children) to update
							$taskIdsToUpdate = [$tareaId];
							$childTasks = Tarea::getChildTasks($tareaId, $conexion);
							foreach ($childTasks as $childTask) {
								$taskIdsToUpdate[] = $childTask->getId();
							}

							$sprintAssociationChanged = Tarea::updateSprintAssociationBulk($taskIdsToUpdate, $sprintId, $conexion);
							if (!$sprintAssociationChanged) {
								throw new Exception("Error al asociar la tarea y sus subtareas al sprint.");
							}
						} catch (Exception $e) {
							throw $e;
						}
					} elseif ($sprintAction === 'disassociate') {
						try {
							// Get all task IDs (parent + children) to update
							$taskIdsToUpdate = [$tareaId];
							$childTasks = Tarea::getChildTasks($tareaId, $conexion);
							foreach ($childTasks as $childTask) {
								$taskIdsToUpdate[] = $childTask->getId();
							}

							$sprintAssociationChanged = Tarea::updateSprintAssociationBulk($taskIdsToUpdate, null, $conexion);
							if (!$sprintAssociationChanged) {
								throw new Exception("Error al desasociar la tarea y sus subtareas del sprint.");
							}
						} catch (Exception $e) {
							throw $e;
						}
					}
				}

				// Update the task object with new properties
				if ($sprintAssociationChanged) {
					// If Sprint association changed, we need to update description/module/historia/nota manually
					// to avoid overwriting the Sprint association we just set
					$query = "UPDATE tareas SET descripcion = ?, id_proyecto_modulo = ?, id_historia = ?, nota = ? WHERE id = ?";
					$stmt = $conexion->prepare($query);
					$success = $stmt->execute([$descripcion, $id_proyecto_modulo, $id_historia, $nota, $tareaId]);
				} else {
					// No Sprint association change, safe to use normal modificar method
					$success = $tarea->modificar($conexion);
				}
				$message = $success ? "Tarea actualizada correctamente." : "Error: No se pudo actualizar la tarea.";

				// If Sprint association changed, add additional context to the message
				if ($sprintAssociationChanged && $success) {
					$action_text = $sprintAction === 'associate' ? 'asociada al' : 'desasociada del';
					$message = "Tarea y sus subtareas {$action_text} sprint correctamente.";
				}
			}

			if ($success) {
				// Get updated task data for UI update
				$updatedTarea = null;
				if ($action !== 'eliminar') {
					// For status changes and edits, get the updated task
					$updatedTarea = Tarea::get($tareaId, $conexion);
				}

				$responseData = [
					'success' => true,
					'message' => $message,
					'action' => $action,
					'tarea_id' => $tareaId,
					'updated_tarea' => null,
					'sprint_association_changed' => $sprintAssociationChanged // Always include this flag
				];

				if ($updatedTarea) {
					if ($action === 'editar') {
						// For edit action, include more task details
						$responseData['updated_tarea'] = [
							'id' => $updatedTarea->getId(),
							'descripcion' => $updatedTarea->getDescripcion(),
							'id_proyecto_modulo' => $updatedTarea->getIdProyectoModulo(),
							'nombre_proyecto_modulo' => $updatedTarea->getNombreProyectoModulo(),
							'id_historia' => $updatedTarea->getIdHistoria(),
							'nombre_historia' => $updatedTarea->getNombreHistoria(),
							'id_proyecto' => $updatedTarea->getIdProyecto(),
							'id_sprint' => $updatedTarea->getIdSprint(),
							'estado' => $updatedTarea->getIdTareaEstado(),
							'nombre_estado' => $updatedTarea->getNombreTareaEstado(),
							'bg_color' => $updatedTarea->getBgColor(),
							'fecha_terminacion' => $updatedTarea->getFechaTerminacion(),
							'nota' => $updatedTarea->getNota()
						];
					} else {
						// For status changes, include basic status info
						$responseData['updated_tarea'] = [
							'id' => $updatedTarea->getId(),
							'estado' => $updatedTarea->getIdTareaEstado(),
							'nombre_estado' => $updatedTarea->getNombreTareaEstado(),
							'bg_color' => $updatedTarea->getBgColor(),
							'fecha_terminacion' => $updatedTarea->getFechaTerminacion()
						];
					}
				}

				echo json_encode($responseData);
			} else {
				echo json_encode(['success' => false, 'message' => $message]);
			}
			exit;

		} catch (Exception $e) {
			error_log("Error in AJAX action '$action' for tarea ID $tareaId: " . $e->getMessage());
			echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
			exit;
		}
	}



	// Handle non-AJAX POST actions (fallback for compatibility)
	$tareaId = filter_input(INPUT_POST, 'tarea_id', FILTER_VALIDATE_INT);

	if (!$tareaId) {
		$_SESSION['flash_message_error'] = "Error: ID de tarea inválido.";
		header('Location: ltareas');
		exit;
	}

	try {

		if ($action === 'marcar_terminada') {
			$success = Tarea::marcarTerminada($tareaId, $conexion);

			if ($success) {
				$_SESSION['flash_message_success'] = "Tarea marcada como terminada correctamente.";
			} else {
				$_SESSION['flash_message_error'] = "Error: No se pudo marcar la tarea como terminada.";
			}
		} elseif ($action === 'marcar_en_progreso') {
			$success = Tarea::marcarEnProgreso($tareaId, $conexion);

			if ($success) {
				$_SESSION['flash_message_success'] = "Tarea marcada como en progreso correctamente.";
			} else {
				$_SESSION['flash_message_error'] = "Error: No se pudo marcar la tarea como en progreso.";
			}
		} elseif ($action === 'eliminar') {
			$success = Tarea::eliminar($tareaId, $conexion);

			if ($success) {
				$_SESSION['flash_message_success'] = "Tarea eliminada correctamente.";
			} else {
				$_SESSION['flash_message_error'] = "Error: No se pudo eliminar la tarea.";
			}
		} else {
			$_SESSION['flash_message_error'] = "Error: Acción no válida.";
		}
	} catch (Exception $e) {
		$_SESSION['flash_message_error'] = "Error: " . $e->getMessage();
	}

	// Redirect back to the task list page after processing
	header('Location: ltareas' . ($filtro_estado ? "?estado=$filtro_estado" : ""));
	exit;
}
#endregion Handle POST Actions

#region region Fetch Tasks
try {
	$parametros = [];

	// Add filter by state if provided
	if ($filtro_estado) {
		$parametros['id_tarea_estado'] = $filtro_estado;
	}

	// Handle explicit request to clear project filter
	if ($clear_project_filter) {
		unset($_SESSION['filtro_proyecto_id']);
	}

	// Check for project filter from session (from project navigation)
	$filtro_proyecto_id = null;
	$nombre_proyecto_filtro = null;
	if (isset($_SESSION['filtro_proyecto_id'])) {
		$filtro_proyecto_id = $_SESSION['filtro_proyecto_id'];
		$parametros['id_proyecto'] = $filtro_proyecto_id;

		// Get project name for display
		try {
			$proyecto = Proyecto::get($filtro_proyecto_id, $conexion);
			$nombre_proyecto_filtro = $proyecto ? $proyecto->getDescripcion() : 'Proyecto no encontrado';
		} catch (Exception $e) {
			error_log("Error getting project name for ID $filtro_proyecto_id: " . $e->getMessage());
			$nombre_proyecto_filtro = 'Proyecto no encontrado';
		}

		// Keep the session filter active for continued navigation within the project context
		// Only clear it when explicitly requested (e.g., when user navigates away from project context)
	}

	// Get active sprint data for Sprint panel
	$active_sprint = null;
	$sprint_tasks = [];
	$sprint_tasks_organized = [];
	try {
		// Use project-specific sprint loading when a project is selected
		if ($filtro_proyecto_id) {
			$active_sprint = Sprint::getActiveSprintByProject($filtro_proyecto_id, $conexion);
		} else {
			$active_sprint = Sprint::getActiveSprint($conexion);
		}
	} catch (Exception $e) {
		error_log("Error fetching active sprint: " . $e->getMessage());
		$active_sprint = null;
	}

	// Only fetch tasks if a project is selected
	if ($filtro_proyecto_id) {
		$tareas = Tarea::get_list($parametros, $conexion);

		// If there's an active sprint, separate sprint tasks from regular tasks
		if ($active_sprint) {
			$regular_tasks = [];
			$sprint_tasks = [];

			foreach ($tareas as $tarea) {
				$tareaSprintId = $tarea->getIdSprint();
				$activeSprintId = $active_sprint->getId();

				if ($tareaSprintId !== null && $tareaSprintId == $activeSprintId) {
					$sprint_tasks[] = $tarea;
				} else {
					$regular_tasks[] = $tarea;
				}
			}

			// Update $tareas to only contain non-sprint tasks for the main list
			$tareas = $regular_tasks;

			// Organize sprint tasks hierarchically
			$sprint_tasks_organized = organizarTareasJerarquicamente($sprint_tasks);
		}
	} else {
		// No project selected - show empty state
		$tareas = [];
	}

	// Get TareaAgente counts for each tarea to determine button states
	// Include counts for both regular tasks and sprint tasks
	$tareas_agentes_counts = [];
	$all_tasks_for_counts = $tareas; // Start with regular tasks

	// Add sprint tasks to the count calculation if they exist
	if (isset($sprint_tasks) && !empty($sprint_tasks)) {
		$all_tasks_for_counts = array_merge($all_tasks_for_counts, $sprint_tasks);
	}

	foreach ($all_tasks_for_counts as $tarea) {
		try {
			$tareas_agentes_counts[$tarea->getId()] = TareaAgente::countByTareaId($tarea->getId(), $conexion);
		} catch (Exception $e) {
			error_log("Error counting TareaAgente for tarea ID " . $tarea->getId() . ": " . $e->getMessage());
			$tareas_agentes_counts[$tarea->getId()] = 0; // Default to 0 on error
		}
	}

	// Organize tasks hierarchically
	$tareas_organizadas = organizarTareasJerarquicamente($tareas);

} catch (PDOException $e) {
	error_log("Database error fetching tasks: " . $e->getMessage());
	$error_display = 'show';
	$error_text = "Error de base de datos al obtener la lista de tareas.";
	$tareas_organizadas = [];
} catch (Exception $e) {
	error_log("Error fetching tasks: " . $e->getMessage());
	$error_display = 'show';
	$error_text = "Ocurrió un error inesperado al obtener la lista de tareas: " . $e->getMessage();
	$tareas_organizadas = [];
}

#region Fetch Active Agents
$agentes_activos = [];
try {
	// Get all active agents (estado = 1)
	$all_agentes = Agente::get_list($conexion, true);

	// Filter out expired agents (expiro = 0 means not expired)
	foreach ($all_agentes as $agente) {
		if (($agente->getExpiro() ?? 0) === 0) {
			$agentes_activos[] = $agente;
		}
	}
} catch (Exception $e) {
	error_log("Error fetching active agents: " . $e->getMessage());
	$agentes_activos = [];
}
#endregion Fetch Active Agents

/**
 * Organiza las tareas en una estructura jerárquica.
 *
 * @param array $tareas Array de objetos Tarea.
 * @return array Array organizado con tareas padre e hijas.
 */
function organizarTareasJerarquicamente(array $tareas): array
{
	$tareas_por_id = [];
	$tareas_padre = [];
	$tareas_hijas = [];

	// Indexar tareas por ID y separar padres de hijas
	foreach ($tareas as $tarea) {
		$tareas_por_id[$tarea->getId()] = $tarea;

		if ($tarea->getIdTareaPadre() === null) {
			// Es una tarea padre (sin padre)
			$tareas_padre[] = $tarea;
		} else {
			// Es una tarea hija
			$id_padre = $tarea->getIdTareaPadre();
			if (!isset($tareas_hijas[$id_padre])) {
				$tareas_hijas[$id_padre] = [];
			}
			$tareas_hijas[$id_padre][] = $tarea;
		}
	}

	// Construir estructura jerárquica
	$resultado = [];
	foreach ($tareas_padre as $tarea_padre) {
		$item = [
			'tarea' => $tarea_padre,
			'hijas' => $tareas_hijas[$tarea_padre->getId()] ?? []
		];
		$resultado[] = $item;
	}

	// Agregar tareas huérfanas (hijas cuyo padre no existe en la lista actual)
	foreach ($tareas_hijas as $id_padre => $hijas) {
		if (!isset($tareas_por_id[$id_padre])) {
			// El padre no está en la lista actual, agregar las hijas como independientes
			foreach ($hijas as $tarea_huerfana) {
				$resultado[] = [
					'tarea' => $tarea_huerfana,
					'hijas' => []
				];
			}
		}
	}

	return $resultado;
}
#endregion Fetch Tasks

function renderNoteIcon(?string $nota): string
{
	if ($nota !== null && trim($nota) !== '' && trim($nota) !== 'N/A') {
		return ' <i class="fa fa-circle-exclamation text-info ms-1" title="Esta tarea tiene notas" style="font-size: 0.9em;"></i>';
	}
	return '';
}

require_once __ROOT__ . '/views/admin/ltareas.view.php';
