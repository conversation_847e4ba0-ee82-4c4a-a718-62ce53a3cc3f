<?php
/**
 * AJAX endpoint to get agentes for dropdown population
 * Used by ltareas.view.php modal
 */

#region region INIT
declare(strict_types=1);

use App\classes\Agente;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';
#endregion region INIT

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en ltareas.php.");
	die('Error crítico: No se pudo conectar a la base de datos.');
}

// Set content type to JSON
header('Content-Type: application/json');

// Initialize response
$response = [
    'success' => false,
    'message' => 'Error al cargar agentes',
    'agentes' => []
];

// Check if this is a valid request
if ($_SERVER['REQUEST_METHOD'] === 'GET' || $_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Get active agentes that have not expired
        $agentes = Agente::get_list($conexion, true); // Only active agents

        // Convert objects to arrays for JSON response, filtering out expired agents
        $agentesArray = [];
        foreach ($agentes as $agente) {
            // Only include agents where expiro = 0 (not expired)
            if (($agente->getExpiro() ?? 0) === 0) {
                $agentesArray[] = [
                    'id' => $agente->getId(),
                    'descripcion' => $agente->getDescripcion(),
                    'balance' => $agente->getBalance() ?? 0.0
                ];
            }
        }
        
        $response = [
            'success' => true,
            'message' => 'Agentes cargados correctamente',
            'agentes' => $agentesArray
        ];
        
    } catch (Exception $e) {
        error_log("Error loading agentes: " . $e->getMessage());
        $response = [
            'success' => false,
            'message' => 'Error al cargar los agentes: ' . $e->getMessage(),
            'agentes' => []
        ];
    }
} else {
    $response = [
        'success' => false,
        'message' => 'Método de solicitud no válido',
        'agentes' => []
    ];
}

// Output JSON response
echo json_encode($response);
exit;
